import * as cron from 'node-cron';
import Accounts from '../models/accounts';
import MudaPayment from './MudaPayment';
import PollingBridgeSwapService from '../services/polling.bridgeSwap.service';
import PollingPayoutsService from '../services/polling.payouts.service';
import RouteExecutionService from '../services/RouteExecutionService';
const pollingBridgeSwapService = new PollingBridgeSwapService();
const routeExecutionService = new RouteExecutionService();
class CronService {
    constructor() {
        console.log("cron initiated==>")
        this.scheduleEveryThirtySeconds();
        this.Every45Seconds();
        this.scheduleEveryTwelveHours();
        this.scheduleBridgeSwapPolling();
        this.scheduleUniversalOfframpExecution();
        this.schedueleveryminute
        MudaPayment.getJWT();


    }
    private schedueleveryminute() {
        cron.schedule('*/1 * * * *', this.everyMinuteTask);
    }

    private scheduleEveryTwelveHours() {
        cron.schedule('0 */12 * * *', this.everyTwelveHoursTask);
    }
    /**
     * Schedule bridge/swap quote polling every 5 minutes
     */
    private scheduleBridgeSwapPolling() {
        // Run every 5 minutes: 0 */5 * * * *
        cron.schedule('0 */5 * * * *', () => {
            this.bridgeSwapPollingTask();
        });

        console.log('✅ Bridge/swap polling scheduled to run every 5 minutes');
    }

    /**
     * Schedule universal off-ramp route execution every 3 minutes
     */
    private scheduleUniversalOfframpExecution() {
        // Run every 3 minutes: 0 */3 * * * *
        cron.schedule('0 */3 * * * *', () => {
            this.universalOfframpExecutionTask();
        });

        console.log('✅ Universal off-ramp execution scheduled to run every 3 minutes');
    }

    /**
     * Task to check pending bridge/swap quotes
     */
    private bridgeSwapPollingTask = async () => {
        try {
            console.log('🔄 Starting scheduled bridge/swap quote polling...');
            await pollingBridgeSwapService.checkPendingBridgeSwapQuotes();
            console.log('✅ Scheduled bridge/swap quote polling completed');
        } catch (error: any) {
            console.error('❌ Error in scheduled bridge/swap polling:', error.message);
        }
    };

    /**
     * Task to execute pending universal off-ramp routes
     */
    private universalOfframpExecutionTask = async () => {
        try {
            console.log('🌍 Starting scheduled universal off-ramp execution...');
            await this.checkPendingUniversalRoutes();
            console.log('✅ Scheduled universal off-ramp execution completed');
        } catch (error: any) {
            console.error('❌ Error in scheduled universal off-ramp execution:', error.message);
        }
    };

    /**
     * Check and execute pending universal off-ramp routes
     */
    private async checkPendingUniversalRoutes() {
        try {
            // Get all routes that need execution
            const pendingRoutes = await routeExecutionService.callRawQuery(`
                SELECT ur.quote_id, ur.current_step, ur.total_steps, ur.overall_status,
                       q.status as quote_status, q.pay_in_status
                FROM universal_routes ur
                INNER JOIN quotes q ON ur.quote_id = q.transId
                WHERE ur.overall_status IN ('pending', 'executing')
                AND q.status NOT IN ('FAILED', 'EXPIRED', 'CANCELLED', 'SUCCESSFUL')
                ORDER BY ur.created_at ASC
                LIMIT 10
            `);

            console.log(`🔍 Found ${pendingRoutes.length} pending universal routes to process`);

            for (const route of pendingRoutes) {
                try {
                    console.log(`🚀 Processing route ${route.quote_id} (step ${route.current_step}/${route.total_steps})`);
                    await routeExecutionService.executeNextStep(route.quote_id);
                } catch (error: any) {
                    console.error(`❌ Error processing route ${route.quote_id}:`, error.message);
                    // Continue with other routes even if one fails
                }
            }
        } catch (error: any) {
            console.error('❌ Error checking pending universal routes:', error.message);
        }
    }
    private everyMinuteTask() {
        console.log('Task running every minute, check pending payouts');
        new PollingPayoutsService().checkPendingDirectPayouts();
    }

    private everyTwelveHoursTask() {
        console.log('Task running every 12 hours, get muda key');
        // Add your logic to get muda key here
        MudaPayment.getJWT();
    }

    private Every45Seconds() {
        cron.schedule('*/45 * * * * *', this.scheduleEvery45Seconds);
    }


    private scheduleEveryThirtySeconds() {
        cron.schedule('*/59 * * * * *', this.everyThirtySecondsTask);
    }


    private scheduleEvery45Seconds() {
        console.log('Task running every 59 seconds, check pending deposits');
        new Accounts().expirePendingQuotes()
    }

    private everyThirtySecondsTask() {
        console.log('Task running every 45 seconds, check pending deposits');
        new Accounts().getQuotestatus()
    }
}

export default CronService;
