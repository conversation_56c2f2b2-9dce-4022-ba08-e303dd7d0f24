import Model from "../helpers/model";
import rhinoClient from "../helpers/Rhino";
import { get } from "../helpers/httpRequest";
import { mapToRhinoChain } from "../helpers/rhinoTypes";

// Interface for polling response format
interface PollingResponse {
    statusCode: number;    // 200, 400, 202, 203
    transStatus: string;   // "SUCCESS", "FAILED", "PENDING", "ONHOLD"
    description: string;   // Human-readable description
    data: any;            // Original response data
}

// Interface for Rhino deposit address history response
interface RhinoDepositHistoryResponse {
    depositAddress: string;
    depositChain: string;
    bridges: Array<{
        _id: string;
        tokenSymbol: string;
        tokenAddress: string;
        amount: string;
        amountUsd: number;
        amountWei: string;
        history: {
            state: "EXECUTED" | "PENDING" | "FAILED" | "CANCELLED";
            _tag: string;
            _id: string;
            chainIn: string;
            chainOut: string;
            amountIn: string;
            amountInUsd: number;
            amountOut: string;
            amountOutUsd: number;
            recipient: string;
            depositor: string;
            createdAt: string;
            commitmentDate: string;
            depositTxHash: string;
            withdrawTxHash?: string;
            withdrawCommittedAt?: string;
        };
        _tag: string;
    }>;
}

class PollingBridgeSwapService extends Model {
    constructor() {
        super();
    }

    /**
     * Main method to check all pending bridge/swap quotes
     * This should be called by the cron job every 6 minutes
     */
    public async checkPendingBridgeSwapQuotes(): Promise<void> {
        try {
            console.log('🔹 Starting bridge/swap quote polling check...');

            // Get all quotes with status 'QUOTE_CONFIRMED' that are older than 1 minute
            // Join with bridge_swap_quote_details to get the necessary information
            const pendingQuotes: any = await this.callRawQuery(
                `SELECT q.*, bsd.from_chain, bsd.to_chain, bsd.from_token, bsd.to_token, 
                        bsd.depositor_address, bsd.destination_address, bsd.smart_deposit_address,
                        bsd.estimated_duration, bsd.rhino_quote_id, bsd.operation_type
                 FROM quotes q
                 INNER JOIN bridge_swap_quote_details bsd ON q.transId = bsd.quote_id
                 WHERE q.status IN ('QUOTE_CONFIRMED', 'PENDING') 
                 AND q.pay_in_status IN ('PENDING_DEPOSIT', 'PENDING')
                 AND q.transaction_type IN ('bridge', 'swap')
                 AND q.created_on <= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
                 AND bsd.smart_deposit_address IS NOT NULL
                 AND bsd.smart_deposit_address != ''
                 ORDER BY q.created_on ASC`
            );

            console.log(`🔹 Found ${pendingQuotes.length} pending bridge/swap quotes to check`);

            for (const quote of pendingQuotes) {
                try {
                    await this.checkSingleBridgeSwapQuote(quote);
                } catch (error: any) {
                    console.error(`❌ Error checking quote ${quote.transId}:`, error.message);
                    // Continue with other quotes even if one fails
                }
            }

            console.log('✅ Bridge/swap quote polling check completed');
        } catch (error: any) {
            console.error('❌ Error in checkPendingBridgeSwapQuotes:', error.message);
        }
    }

    /**
     * Check a single bridge/swap quote status
     */
    private async checkSingleBridgeSwapQuote(quote: any): Promise<void> {
        const { 
            transId, 
            client_reference_id, 
            from_chain, 
            to_chain, 
            from_token, 
            to_token,
            depositor_address,
            destination_address,
            smart_deposit_address,
            estimated_duration,
            rhino_quote_id,
            operation_type
        } = quote;

        console.log(`🔹 Checking quote ${transId} (${client_reference_id}) - ${operation_type} ${from_token} ${from_chain} → ${to_token} ${to_chain}`);

        // Use the smart deposit address from the details table
        const depositAddress = smart_deposit_address;
        const depositChain = mapToRhinoChain(from_chain); // Map to Rhino format

        console.log(`🔹 Chain mapping: ${from_chain} → ${depositChain}`);

        if (!depositAddress || !depositChain) {
            console.error(`❌ Missing deposit address or chain for quote ${transId}`);
            console.error(`❌ Deposit address: ${depositAddress}, Chain: ${depositChain}`);
            return;
        }

        console.log(`🔹 Checking deposit address ${depositAddress} on chain ${depositChain}`);

        // Check the deposit address history with Rhino
        const pollingResponse = await this.checkRhinoDepositStatus(depositAddress, depositChain, transId);

        // Update quote status based on response
        await this.updateQuoteStatus(quote, pollingResponse);
    }

    /**
     * Check Rhino deposit address status and history
     * FIXED LOGIC: Status is always 200 OK, we check bridges array for actual status
     */
    private async checkRhinoDepositStatus(
        depositAddress: string, 
        depositChain: string, 
        transId: string
    ): Promise<PollingResponse> {
        try {
            console.log(`🔹 Checking Rhino deposit status for ${depositAddress} on ${depositChain}`);

            // Use the Rhino client to get deposit address history
            const response = await rhinoClient.getDepositAddressHistory(depositAddress, depositChain);

            if (!response.success) {
                console.error(`❌ Failed to get deposit address history:`, response);
                return this.mapPollingResponse(500, "Failed to check deposit status", response);
            }

            const historyData: RhinoDepositHistoryResponse = response.data;
            console.log(`🔹 Deposit history response:`, JSON.stringify(historyData, null, 2));

            // FIXED LOGIC: Check bridges array
            if (!historyData.bridges || historyData.bridges.length === 0) {
                // Empty bridges array = no deposit made yet (still waiting)
                console.log(`🔹 No deposit detected yet for address ${depositAddress} - still pending`);
                return this.mapPollingResponse(202, "No deposit detected yet - still pending", historyData);
            }

            // Bridges array has entries - check the state of the most recent bridge
            const latestBridge = historyData.bridges[0];
            const bridgeState = latestBridge.history.state;
            const bridgeTag = latestBridge._tag;

            console.log(`🔹 Latest bridge state: ${bridgeState}, tag: ${bridgeTag} for quote ${transId}`);

            if (bridgeState === "EXECUTED" && bridgeTag === "accepted") {
                // Bridge completed successfully - both state and tag must be correct
                console.log(`✅ Bridge executed successfully for quote ${transId} (state: EXECUTED, tag: accepted)`);
                return this.mapPollingResponse(200, "Bridge executed successfully", historyData);
            } else {
                // Any other state (PENDING, etc.) - still processing
                console.log(`🔹 Bridge still processing (state: ${bridgeState}, tag: ${bridgeTag}) for quote ${transId}`);
                return this.mapPollingResponse(202, `Bridge ${bridgeState.toLowerCase()}`, historyData);
            }

        } catch (error: any) {
            console.error(`❌ Error checking Rhino deposit status:`, error);
            return this.mapPollingResponse(500, `Error checking deposit status: ${error.message}`);
        }
    }

    /**
     * Update quote status based on polling response
     */
    private async updateQuoteStatus(quote: any, pollingResponse: PollingResponse): Promise<void> {
        const { transId, client_reference_id, created_at } = quote;
        const { statusCode, transStatus, description, data } = pollingResponse;

        try {
            let updateData: any = {};
            let logMessage = "";

            if (statusCode === 200) {
                // Bridge executed successfully
                updateData = {
                    status: 'CRYPTO_RECEIVED',
                    pay_in_status: 'SUCCESSFUL',
                    reason: 'Bridge executed successfully',
                };
                logMessage = `✅ Bridge executed successfully for quote ${transId}`;
                console.log(logMessage);

            } else if (statusCode === 202) {
                // Still pending - check if we should expire based on expires_at field
                const currentTime = new Date();
                const expiresAt = new Date(quote.expires_at);
                
                console.log(`🔹 Checking expiry for quote ${transId}:`, {
                    currentTime: currentTime.toISOString(),
                    expiresAt: expiresAt.toISOString(),
                    isExpired: currentTime > expiresAt
                });

                if (currentTime > expiresAt) {
                    // Quote has expired based on expires_at field
                    updateData = {
                        status: 'EXPIRED',
                        pay_in_status: 'FAILED',
                        reason: 'Quote expired based on expires_at timestamp',
                    };
                    logMessage = `⏰ Quote expired for ${transId} at ${expiresAt.toISOString()}`;
                    console.log(logMessage);
                } else {
                    // Still within valid time - update with pending status
                    updateData = {
                        status: 'PENDING',
                        pay_in_status: 'PENDING',
                        reason: `Bridge ${description}`,
                    };
                    logMessage = `🔹 Bridge still pending for quote ${transId}: ${description}`;
                    console.log(logMessage);
                }

            } else {
                // Error status
                updateData = {
                    status: 'FAILED',
                    pay_in_status: 'FAILED',
                    reason: `Polling error: ${description}`,
                };
                logMessage = `❌ Polling error for quote ${transId}: ${description}`;
                console.log(logMessage);
            }

            // Update the quote in database
            if (Object.keys(updateData).length > 0) {
                await this.updateData('quotes', `transId = '${transId}'`, updateData);
                console.log(`✅ Updated quote ${transId} status to ${updateData.status}`);
            }

        } catch (error: any) {
            console.error(`❌ Error updating quote ${transId} status:`, error.message);
        }
    }

    /**
     * Map response to standard polling response format
     */
    private mapPollingResponse(statusCode: number, description: string, data: any = null): PollingResponse {
        let transStatus: string;

        if (statusCode === 200) {
            transStatus = "SUCCESS";
        } else if (statusCode === 400) {
            transStatus = "FAILED";
        } else if (statusCode === 202) {
            transStatus = "PENDING";
        } else {
            transStatus = "ONHOLD";
        }

        return {
            statusCode,
            transStatus,
            description,
            data
        };
    }
}

export default PollingBridgeSwapService;