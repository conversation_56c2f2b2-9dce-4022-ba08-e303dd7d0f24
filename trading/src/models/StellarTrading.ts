import Model from '../helpers/model';
import StellarService from '../helpers/StellarService';
import { TradingEvent } from '../utils/messageBus';
import { CallbackData, systemProductCodes, TransactionInterface, TransactionInterfaceMini } from '../helpers/interface';
import axios from 'axios';

const stellar = new StellarService();

// Central trading account environment variables
const TRADING_ACCOUNT_PUBLIC = process.env.TRADING_ACCOUNT_PUBLIC || "";
const TRADING_ACCOUNT_SECRET = process.env.TRADING_ACCOUNT_SECRET || "";
// Helper function to format date for MySQL
function formatDateForMySQL(date: Date = new Date()): string {
    return date.toISOString().replace('T', ' ').replace('Z', '').split('.')[0];
}

export type WebhookData = {
    type: string;
    statusCode: number;
    message: string;
    client_id: string;
    trans_type: string;
    timestamp: string;
    reference_id: string;
    status: string;
    amount: string;
    fee: string;
    currency: string;
    sender_account: string;
    receiver_account: string;
    transaction_id: string;
    meta: string;
}

interface TradeOffer {
    clientId: string;
    baseAsset: string;
    counterAsset: string;
    amount: string;
    orderType: 'buy' | 'sell';
    type: 'market' | 'limit' | 'rail';  // New: order type (market, limit, or rail)
    price?: string;            // Required for limit orders
    slippage?: number;         // Required for market orders (0-5%)
    referenceId?: string;      // Reference ID for rail orders
    payoutData?: any;          // Payout details for rail orders
}

interface SwapOrder {
    clientId: string;
    sourceAsset: string;
    destinationAsset: string;
    sourceAmount: string;
    minDestAmount: string;
}

class StellarTrading extends Model {
    constructor() {
        super();
    }

    /**
     * Validate order parameters and type
     */
    private validateOrderParams(data: TradeOffer) {
        const { type, price, slippage } = data;

        if (type === 'limit' && !price) {
            return { valid: false, error: 'Price is required for limit orders' };
        }

        if (type === 'market' && (slippage === undefined || slippage < 0 || slippage > 5)) {
            return { valid: false, error: 'Slippage is required for market orders and must be between 0 and 5%' };
        }

        if (type !== 'market' && type !== 'limit') {
            return { valid: false, error: 'Order type must be either "market" or "limit"' };
        }

        return { valid: true };
    }

    /**
     * Calculate execution price and get orders to fill
     * UNIFIED LOGIC: Market and Limit orders use same logic
     * Market order = Limit order with slippage tolerance
     * Limit order = Market order with strict price matching
     */
    private async calculateExecutionPrice(
        type: 'market' | 'limit',
        orderType: 'buy' | 'sell',
        baseAsset: string,
        counterAsset: string,
        amount: string,
        clientId: string,
        price?: string,
        slippage?: number
    ) {
        // For limit orders, first check if there are opposite orders at the same rate or better
        if (type === 'limit' && price) {
            // Get orderbook for limit order matching
            const orderBook = await this.getOrderBook(baseAsset, counterAsset, clientId);
            if (orderBook.status !== 200) {
                return { executionPrice: price, ordersToFill: [], averagePrice: price, canFill: false, partialFill: false };
            }

            const availableOrders = orderType === 'buy' ? orderBook.data.sellOrders : orderBook.data.buyOrders;

            if (!availableOrders || availableOrders.length === 0) {
                return { executionPrice: price, ordersToFill: [], averagePrice: price, canFill: false, partialFill: false };
            }

            // Calculate cumulative depth at the specified price or better
            let remainingAmount = parseFloat(amount);
            let totalCost = 0;
            let totalAmount = 0;
            const ordersToFill: any[] = [];

            for (const order of availableOrders) {
                if (remainingAmount <= 0) break;

                // Skip own orders
                if (order.client_id.toString() === clientId.toString()) continue;

                // STRICT PRICE MATCHING for limit orders
                const orderPrice = parseFloat(order.price);
                let priceMatches = false;

                if (orderType === 'buy') {
                    // For buy limit orders: only match sell orders at our price or better (lower)
                    priceMatches = orderPrice <= parseFloat(price);
                } else {
                    // For sell limit orders: only match buy orders at our price or better (higher)
                    priceMatches = orderPrice >= parseFloat(price);
                }

                if (!priceMatches) continue;

                // Calculate available amount and fill
                const orderAvailable = parseFloat(order.amount) - parseFloat(order.filled_amount || '0');
                if (orderAvailable <= 0) continue;

                const fillAmount = Math.min(remainingAmount, orderAvailable);
                const fillCost = fillAmount * orderPrice;

                ordersToFill.push({
                    order: order,
                    fillAmount: fillAmount,
                    price: orderPrice,
                    fillCost: fillCost
                });

                totalAmount += fillAmount;
                totalCost += fillCost;
                remainingAmount -= fillAmount;
            }

            const canFill = remainingAmount === 0;
            const partialFill = totalAmount > 0 && remainingAmount > 0;
            const averagePrice = totalAmount > 0 ? totalCost / totalAmount : parseFloat(price);

            return {
                executionPrice: price,
                ordersToFill,
                averagePrice: averagePrice.toFixed(7),
                canFill,
                partialFill
            };
        }

        // For market orders, calculate actual execution price from orderbook depth
        const orderBook = await this.getOrderBook(baseAsset, counterAsset, clientId);
        if (orderBook.status !== 200) {
            throw new Error('Could not get market price for market order');
        }

        const availableOrders = orderType === 'buy' ? orderBook.data.sellOrders : orderBook.data.buyOrders;

        console.log(`📊 Market ${orderType} order: Looking for ${orderType === 'buy' ? 'sell' : 'buy'} orders`);
        console.log(`📊 Available ${orderType === 'buy' ? 'sell' : 'buy'} orders:`, availableOrders.length);
        console.log(`📊 Trading pair: ${baseAsset}/${counterAsset}`);
        console.log(`📊 Market order wants: ${orderType} ${amount} ${baseAsset} for ${counterAsset}`);

        if (!availableOrders || availableOrders.length === 0) {
            throw new Error(`No ${orderType === 'buy' ? 'sell' : 'buy'} orders available for ${baseAsset}/${counterAsset}`);
        }

        // CRITICAL FIX: Verify that available orders match the market order intent
        const firstOrder = availableOrders[0];
        console.log(`📊 First available order:`, {
            order_type: firstOrder.order_type,
            selling_asset: firstOrder.selling_asset,
            buying_asset: firstOrder.buying_asset,
            price: firstOrder.price
        });

        // Validate that the orders are actually compatible with our market order
        if (orderType === 'buy') {
            // Market BUY order wants to buy baseAsset with counterAsset
            // We need SELL orders that are selling baseAsset for counterAsset
            if (firstOrder.selling_asset !== baseAsset || firstOrder.buying_asset !== counterAsset) {
                throw new Error(`Order mismatch: Market buy order wants ${baseAsset}/${counterAsset} but found orders for ${firstOrder.selling_asset}/${firstOrder.buying_asset}`);
            }
        } else {
            // Market SELL order wants to sell baseAsset for counterAsset  
            // We need BUY orders that are buying baseAsset with counterAsset
            if (firstOrder.buying_asset !== baseAsset || firstOrder.selling_asset !== counterAsset) {
                throw new Error(`Order mismatch: Market sell order wants ${baseAsset}/${counterAsset} but found orders for ${firstOrder.buying_asset}/${firstOrder.selling_asset}`);
            }
        }

        // Calculate cumulative depth and find orders needed to fill
        // CRITICAL FIX: Market order amount interpretation
        let remainingAmount = parseFloat(amount);
        let totalCost = 0;
        let totalAmount = 0;
        const ordersToFill: any[] = [];
        let worstPrice = 0;

        console.log(`📊 Market ${orderType} order amount interpretation:`);
        console.log(`   Market order wants: ${amount} ${baseAsset} for ${counterAsset}`);
        console.log(`   Remaining amount to fill: ${remainingAmount}`);

        for (const order of availableOrders) {
            if (remainingAmount <= 0) break;

            // Skip own orders
            if (order.client_id.toString() === clientId.toString()) continue;

            // CORRECTED: Think about order matching and execution
            let orderAvailable: number;
            let fillAmount: number;
            let fillCost: number;

            if (orderType === 'buy') {
                // Market BUY order: We want to buy baseAsset (USDT) with counterAsset (UGX)
                // We're matching against SELL orders (people selling baseAsset)
                // order.amount = how much baseAsset the sell order has available to sell
                orderAvailable = parseFloat(order.amount) - parseFloat(order.filled_amount || '0');
                fillAmount = Math.min(remainingAmount, orderAvailable); // Amount of baseAsset we're buying
                fillCost = fillAmount * parseFloat(order.price); // Cost in counterAsset (UGX)
                
                console.log(`   📈 Taking SELL order ${order.order_id}: ${orderAvailable} ${baseAsset} available @ ${order.price} ${counterAsset}/${baseAsset}`);
                console.log(`   💰 Market BUY: Taking ${fillAmount} ${baseAsset} for ${fillCost} ${counterAsset}`);
            } else {
                // Market SELL order: We want to sell baseAsset (USDT) for counterAsset (UGX)
                // We're matching against BUY orders (people wanting to buy baseAsset)
                // order.amount = how much baseAsset the buy order wants to buy
                orderAvailable = parseFloat(order.amount) - parseFloat(order.filled_amount || '0');
                fillAmount = Math.min(remainingAmount, orderAvailable); // Amount of baseAsset we're selling
                fillCost = fillAmount * parseFloat(order.price); // Revenue in counterAsset (UGX)
                
                console.log(`   📉 Taking BUY order ${order.order_id}: wants ${orderAvailable} ${baseAsset} @ ${order.price} ${counterAsset}/${baseAsset}`);
                console.log(`   💰 Market SELL: Giving ${fillAmount} ${baseAsset} for ${fillCost} ${counterAsset}`);
            }

            if (orderAvailable <= 0) continue;

            ordersToFill.push({
                order: order,
                fillAmount: fillAmount,
                price: parseFloat(order.price),
                fillCost: fillCost
            });

            totalAmount += fillAmount;
            totalCost += fillCost;
            remainingAmount -= fillAmount;
            worstPrice = parseFloat(order.price); // This is the last (worst) price

            console.log(`   ✅ Fill: ${fillAmount} ${baseAsset} for ${fillCost} ${counterAsset} (remaining: ${remainingAmount})`);
        }

        if (totalAmount === 0) {
            throw new Error('Insufficient liquidity to fill market order');
        }

        // Calculate average execution price
        const averagePrice = totalCost / totalAmount;

        // UNIFIED SLIPPAGE LOGIC: Apply slippage tolerance (default 0% for market orders)
        const slippageTolerance = slippage || 0; // Default 0% slippage for market orders
        const maxAllowedPrice = orderType === 'buy'
            ? averagePrice * (1 + slippageTolerance / 100)  // Buy: accept higher price
            : averagePrice * (1 - slippageTolerance / 100); // Sell: accept lower price

        const slippageExceeded = orderType === 'buy'
            ? worstPrice > maxAllowedPrice
            : worstPrice < maxAllowedPrice;

        if (slippageExceeded && slippageTolerance > 0) {
            throw new Error(
                `Slippage exceeded: worst price ${worstPrice} exceeds ${slippageTolerance}% tolerance from average ${averagePrice.toFixed(7)}`
            );
        }

        console.log(`📊 Market order: Average price ${averagePrice.toFixed(7)}, Worst price ${worstPrice}, Total amount ${totalAmount}/${amount}`);

        return {
            executionPrice: worstPrice.toFixed(7), // Worst price for balance checks
            ordersToFill: ordersToFill,
            averagePrice: averagePrice.toFixed(7),
            canFill: remainingAmount === 0,
            partialFill: remainingAmount > 0 && totalAmount > 0
        };
    }

    /**
     * Calculate transfer details based on order type
     * FIXED: Corrected the asset assignment logic for proper trading
     */
    private calculateTransferDetails(
        orderType: 'buy' | 'sell',
        baseAsset: string,
        counterAsset: string,
        baseIssuer: string,
        counterIssuer: string,
        amount: string,
        executionPrice: string
    ) {
        if (orderType === 'sell') {
            // SELL ORDER: Selling baseAsset for counterAsset
            // You transfer baseAsset (what you're selling)
            // You receive counterAsset (what you're buying)
            return {
                transferAsset: baseAsset,           // Asset you need to have (what you're selling)
                transferIssuer: baseIssuer,
                transferAmount: amount,             // Amount of baseAsset you're selling
                sellingAsset: baseAsset,            // What you're selling
                buyingAsset: counterAsset,          // What you want to buy
                sellingIssuer: baseIssuer,
                buyingIssuer: counterIssuer
            };
        } else {
            // BUY ORDER: Buying baseAsset with counterAsset  
            // You transfer counterAsset (what you're paying with)
            // You receive baseAsset (what you're buying)
            return {
                transferAsset: counterAsset,        // Asset you need to have (what you're paying with)
                transferIssuer: counterIssuer,
                transferAmount: (parseFloat(amount) * parseFloat(executionPrice)).toFixed(7), // Amount of counterAsset you need
                sellingAsset: counterAsset,         // What you're selling (paying with)
                buyingAsset: baseAsset,             // What you want to buy
                sellingIssuer: counterIssuer,
                buyingIssuer: baseIssuer
            };
        }
    }

    /**
     * Execute market order immediately by filling multiple orders in a single atomic transaction
     * FIXED: Added market order context to ensure correct asset mapping
     */
    private async executeMarketOrderImmediately(
        clientId: string,
        ordersToFill: any[],
        orderType: 'buy' | 'sell',
        amount: string,
        averagePrice: string,
        baseAsset?: string,
        counterAsset?: string
    ) {
        console.log(`🚀 Executing market ${orderType} order with ${ordersToFill.length} orders in single atomic transaction`);
        console.log(`🚀 Market order context: ${orderType} ${amount} ${baseAsset} for ${counterAsset}`);

        // Ensure baseAsset and counterAsset are defined
        if (!baseAsset || !counterAsset) {
            throw new Error('Base asset and counter asset must be defined for market orders');
        }

        // Get taker keys
        const takerKeys = await this.getDecryptedApiKey(clientId);
        if (!takerKeys) {
            throw new Error('Taker account not found');
        }

        const transId = this.getTransId();
        const recipients: any[] = [];
        const tradeRecords: any[] = [];
        let totalFilledAmount = 0;
        let totalCost = 0;

        // Step 1: DATABASE FIRST - Create trade records with pending status
        console.log(`📝 Step 1: Creating trade records in database FIRST`);

        for (const { order, fillAmount, price, fillCost } of ordersToFill) {
            const tradeId = this.getTransId();

            // CRITICAL FIX: Use market order context to determine correct asset mapping
            let takerSellsAsset: string;
            let takerBuysAsset: string;
            let takerSellsAmount: number;
            let takerBuysAmount: number;

            if (orderType === 'buy') {
                // Market BUY order: taker is buying baseAsset (USDT) with counterAsset (UGX)
                // Taker pays counterAsset, receives baseAsset
                takerSellsAsset = counterAsset;  // What taker pays with (UGX)
                takerBuysAsset = baseAsset;      // What taker receives (USDT)
                takerSellsAmount = fillCost;     // Amount taker pays (UGX cost)
                takerBuysAmount = fillAmount;    // Amount taker receives (USDT)
            } else {
                // Market SELL order: taker is selling baseAsset (USDT) for counterAsset (UGX)
                // Taker pays baseAsset, receives counterAsset
                takerSellsAsset = baseAsset;     // What taker sells (USDT)
                takerBuysAsset = counterAsset;   // What taker receives (UGX)
                takerSellsAmount = fillAmount;   // Amount taker sells (USDT)
                takerBuysAmount = fillCost;      // Amount taker receives (UGX revenue)
            }

            console.log(`📝 Trade details:`, {
                takerSells: `${takerSellsAmount} ${takerSellsAsset}`,
                takerBuys: `${takerBuysAmount} ${takerBuysAsset}`,
                makerOrder: `${order.selling_asset}/${order.buying_asset}`,
                price: price,
                fillCost: fillCost
            });

            // Get issuers for both assets (all assets use the same issuer from .env)
            const takerSellsIssuer = await this.GetTradingIssuer(takerSellsAsset, clientId);
            const takerBuysIssuer = await this.GetTradingIssuer(takerBuysAsset, clientId);

            // Insert trade record with empty stellar_hash (pending)
            // CRITICAL FIX: Use correct amount for trade record
            // Trade record should show the amount of the asset being traded (baseAsset)
            await this.insertData('stellar_trades', {
                trade_id: tradeId,
                order_id: tradeId,
                maker_id: order.client_id,
                taker_id: clientId,
                amount: fillAmount.toFixed(7), // Amount of baseAsset being traded
                price: price.toString(),
                stellar_hash: '', // Empty until blockchain execution
                created_at: formatDateForMySQL()
            });

            // Compose blockchain transfers
            const makerKeys = await this.getDecryptedApiKey(order.client_id);
            if (!makerKeys) {
                throw new Error(`Maker ${order.client_id} keys not found`);
            }

            // FIXED: Use correct asset mapping based on market order context
            // 1. Taker pays what they're selling to TRADER
            recipients.push({
                publicKey: TRADING_ACCOUNT_PUBLIC,
                amount: takerSellsAmount.toFixed(7),
                asset_code: takerSellsAsset,
                asset_issuer: takerSellsIssuer,
                senderSecretKey: takerKeys.secret_key,
                creditPrivateKey: TRADING_ACCOUNT_SECRET
            });

            // 2. TRADER sends what taker is buying to taker
            recipients.push({
                publicKey: takerKeys.public_key,
                amount: takerBuysAmount.toFixed(7),
                asset_code: takerBuysAsset,
                asset_issuer: takerBuysIssuer,
                senderSecretKey: TRADING_ACCOUNT_SECRET,
                creditPrivateKey: takerKeys.secret_key
            });

            // 3. TRADER sends what maker is selling to maker
            recipients.push({
                publicKey: makerKeys.public_key,
                amount: takerBuysAmount.toFixed(7),
                asset_code: order.selling_asset,
                asset_issuer: await this.GetTradingIssuer(order.selling_asset, clientId),
                senderSecretKey: TRADING_ACCOUNT_SECRET,
                creditPrivateKey: makerKeys.secret_key
            });

            totalFilledAmount += takerBuysAmount; // Total baseAsset received
            totalCost += takerSellsAmount;        // Total counterAsset spent

            // Store for later update
            tradeRecords.push({
                tradeId,
                order,
                fillAmount: takerBuysAmount,    // Amount of baseAsset
                counterAmount: takerSellsAmount, // Amount of counterAsset
                price
            });
        }

        // Step 2: BLOCKCHAIN SECOND - Execute all transfers atomically
        console.log(`💫 Step 2: Executing ${recipients.length} transfers on BLOCKCHAIN`);
        const result = await stellar.makeBatchTransfers(transId, 'MARKET_TRADE', recipients);

        if (result.response !== 1) {
            // Blockchain failed - mark trades as failed
            for (const { tradeId } of tradeRecords) {
                await this.updateData('stellar_trades', `trade_id='${tradeId}'`, {
                    stellar_hash: 'FAILED'
                });
            }
            throw new Error(`Atomic trade execution failed: ${result.message}`);
        }

        const stellarHash = result.message;
        console.log(`✅ Blockchain transaction executed: ${stellarHash}`);

        // Step 3: UPDATE THIRD - Update database with blockchain hash and order statuses
        console.log(`📊 Step 3: Updating database with blockchain hash`);
        const executedTrades: any[] = [];

        for (const { tradeId, order, fillAmount, counterAmount, price } of tradeRecords) {
            // Update trade record with blockchain hash
            await this.updateData('stellar_trades', `trade_id='${tradeId}'`, {
                stellar_hash: stellarHash
            });

            // Update maker order
            const newFilledAmount = parseFloat(order.filled_amount || '0') + fillAmount;
            await this.updateData('stellar_orders', `order_id='${order.order_id}'`, {
                filled_amount: newFilledAmount.toFixed(7),
                stellar_hash: stellarHash,
                status: newFilledAmount >= parseFloat(order.amount) ? 'filled' : 'partially_filled'
            });

            executedTrades.push({
                orderId: order.order_id,
                amount: fillAmount,
                price: price,
                cost: counterAmount.toFixed(7),
                hash: stellarHash
            });
        }

        return {
            orderType,
            requestedAmount: amount,
            filledAmount: totalFilledAmount.toFixed(7),
            averagePrice: averagePrice,
            totalCost: totalCost.toFixed(7),
            executedTrades: executedTrades,
            stellarHash: stellarHash,
            fullyFilled: totalFilledAmount >= parseFloat(amount),
            partiallyFilled: totalFilledAmount > 0 && totalFilledAmount < parseFloat(amount)
        };
    }

    /**
     * Preflight check for market orders - check liquidity before submitting
     */
    public async preflightMarketOrder(data: {
        clientId: string;
        baseAsset: string;
        counterAsset: string;
        amount: string;
        orderType: 'buy' | 'sell';
        slippage?: number;
    }) {
        try {
            const { clientId, baseAsset, counterAsset, amount, orderType, slippage = 1 } = data;

            // Get orderbook
            const orderBook = await this.getOrderBook(baseAsset, counterAsset, clientId);
            if (orderBook.status !== 200) {
                return this.makeResponse(400, 'Could not get market data');
            }

            const availableOrders = orderType === 'buy' ? orderBook.data.sellOrders : orderBook.data.buyOrders;

            if (!availableOrders || availableOrders.length === 0) {
                return this.makeResponse(400, `No ${orderType === 'buy' ? 'sell' : 'buy'} orders available`, {
                    hasLiquidity: false,
                    availableVolume: '0',
                    ordersAvailable: 0
                });
            }

            // Calculate how much can be filled
            let remainingAmount = parseFloat(amount);
            let totalCost = 0;
            let totalAmount = 0;
            let ordersNeeded = 0;
            let worstPrice = 0;
            let bestPrice = 0;

            for (const order of availableOrders) {
                if (remainingAmount <= 0) break;

                // Skip own orders
                if (order.client_id.toString() === clientId.toString()) continue;

                const orderAvailable = parseFloat(order.amount) - parseFloat(order.filled_amount || '0');
                if (orderAvailable <= 0) continue;

                const fillAmount = Math.min(remainingAmount, orderAvailable);
                const orderPrice = parseFloat(order.price);
                const fillCost = fillAmount * orderPrice;

                if (ordersNeeded === 0) bestPrice = orderPrice;

                totalAmount += fillAmount;
                totalCost += fillCost;
                remainingAmount -= fillAmount;
                worstPrice = orderPrice;
                ordersNeeded++;
            }

            const canFillCompletely = remainingAmount === 0;
            const availableVolume = totalAmount;
            const averagePrice = totalAmount > 0 ? totalCost / totalAmount : 0;

            // Apply slippage for market orders
            const maxAcceptablePrice = orderType === 'buy'
                ? bestPrice * (1 + slippage / 100)
                : bestPrice * (1 - slippage / 100);

            const withinSlippage = orderType === 'buy'
                ? worstPrice <= maxAcceptablePrice
                : worstPrice >= maxAcceptablePrice;

            return this.makeResponse(200, 'Preflight check completed', {
                hasLiquidity: totalAmount > 0,
                canFillCompletely,
                availableVolume: totalAmount.toFixed(8),
                requestedAmount: amount,
                fillableAmount: totalAmount.toFixed(8),
                fillPercentage: ((totalAmount / parseFloat(amount)) * 100).toFixed(2),
                ordersAvailable: ordersNeeded,
                bestPrice: bestPrice.toFixed(8),
                worstPrice: worstPrice.toFixed(8),
                averagePrice: averagePrice.toFixed(8),
                estimatedCost: totalCost.toFixed(8),
                slippage: slippage,
                withinSlippage,
                warning: !canFillCompletely ? 'Insufficient liquidity - order will be partially filled' : null
            });

        } catch (error: any) {
            console.error('❌ Error in preflight check:', error);
            return this.makeResponse(500, 'Preflight check failed', { error: error.message });
        }
    }

   
    public async createOffer(data: TradeOffer) {
        try {
            console.log('🔹 Creating order:', data);

            const { clientId, baseAsset, counterAsset, amount, price, orderType, type, slippage } = data;

            // Step 1: Validate parameters
            const validation = this.validateOrderParams(data);
            if (!validation.valid) {
                return this.makeResponse(400, validation.error!);
            }

            // Handle rail orders - save as pending
            if (type === 'rail') {
                return this.createPendingRailOrder({
                    clientId,
                    referenceId: data.referenceId || this.getTransId(),
                    sourceAsset: baseAsset,
                    destinationAsset: counterAsset,
                    amount,
                    expectedAmount: undefined,
                    slippage: slippage || 1,
                    payoutData: data.payoutData,
                    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours default
                });
            }

            // Step 2: Validate assets and get issuers
            const baseIssuer = await this.GetTradingIssuer(baseAsset, clientId);
            const counterIssuer = await this.GetTradingIssuer(counterAsset, clientId);

            if (baseAsset === counterAsset && baseIssuer === counterIssuer) {
                return this.makeResponse(400, 'Cannot trade the same asset');
            }

            const baseAssetSupported = await this.isAssetSupported(baseAsset);
            const counterAssetSupported = await this.isAssetSupported(counterAsset);

            if (!baseAssetSupported || !counterAssetSupported) {
                const supportedAssets = await this.getSupportedFiatAssets();
                return this.makeResponse(400, `Only supported assets allowed: ${supportedAssets.join(', ')}`);
            }

            // Step 3: Get client wallet
            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return this.makeResponse(400, 'Client wallet not found');
            }

            // Step 4: Calculate execution price and get orders to fill
            const priceResult = await this.calculateExecutionPrice(
                type, orderType, baseAsset, counterAsset, amount, clientId, price, slippage
            );
            const executionPrice = priceResult.executionPrice;

            // Step 5: Calculate transfer details
            const transferDetails = this.calculateTransferDetails(
                orderType, baseAsset, counterAsset, baseIssuer, counterIssuer, amount, executionPrice.toString()
            );

            // Step 6: Check balance
            const balances = await stellar.getBalance(clientId, [transferDetails.transferAsset]);
            const assetBalance = balances.find((bal: any) => bal.code === transferDetails.transferAsset);
            const availableBalance = assetBalance ? parseFloat(assetBalance.balance) : 0;

            if (availableBalance < parseFloat(transferDetails.transferAmount)) {
                return this.makeResponse(400,
                    `Insufficient ${transferDetails.transferAsset} balance. Available: ${availableBalance}, Required: ${transferDetails.transferAmount}`
                );
            }

            // Step 7: UNIFIED EXECUTION - Market and Limit orders use same logic
            // Market order = Limit order with slippage tolerance
            // Limit order = Market order with strict price matching
            
            if (type === 'market' || type === 'limit') {
                // For both market and limit orders, try to execute immediately
                if (priceResult.canFill || priceResult.partialFill) {
                    const result = await this.executeMarketOrderImmediately(
                        clientId,
                        priceResult.ordersToFill,
                        orderType,
                        amount,
                        priceResult.averagePrice.toString(),
                        baseAsset,
                        counterAsset
                    );
                    
                    // If market order or limit order was fully executed
                    if (result.fullyFilled) {
                        return this.makeResponse(200, `${type} order executed successfully`, result);
                    }
                    
                    // If partially executed, continue to create remaining limit order
                    console.log(`📊 ${type} order partially executed, creating limit order for remaining amount`);
                } else if (type === 'market') {
                    // Market orders must have liquidity
                    return this.makeResponse(400, 'Insufficient liquidity to fill market order');
                }
                
                // For limit orders with no immediate matches, continue to create the order
                // For market orders that were partially filled, create limit order for remainder
            }

            // Step 8: Create limit order
            const orderId = this.getTransId();
            
            // FIXED: amount should always be the base asset amount
            // receivable_amount should be the counter asset amount
            const baseAssetAmount = amount; // User's requested amount (base asset)
            const counterAssetAmount = orderType === 'sell' 
                ? (parseFloat(amount) * parseFloat(executionPrice.toString())).toFixed(7) // Sell: amount * price = counter asset
                : (parseFloat(amount) * parseFloat(executionPrice.toString())).toFixed(7); // Buy: amount * price = counter asset
            
            const orderData = {
                order_id: orderId,
                client_id: clientId,
                stellar_offer_id: orderId,
                selling_asset: transferDetails.sellingAsset,
                buying_asset: transferDetails.buyingAsset,
                amount: baseAssetAmount, // CORRECT: Always the base asset amount
                transfer_amount: transferDetails.transferAmount, // Amount of selling_asset (what user gives up)
                receivable_amount: counterAssetAmount, // Amount of counter_asset (what user expects to receive)
                filled_amount: '0',
                price: executionPrice,
                order_type: orderType,
                status: 'pending',
                stellar_hash: '',
                created_at: formatDateForMySQL()
            };

            console.log('📝 Creating order with details:');
            console.log('   Order Type:', orderType);
            console.log('   Trading Pair:', `${baseAsset}/${counterAsset}`);
            console.log('   User Requested Amount:', amount);
            console.log('   Price:', executionPrice);
            console.log('   Selling Asset:', transferDetails.sellingAsset);
            console.log('   Buying Asset:', transferDetails.buyingAsset);
            console.log('   Transfer Asset:', transferDetails.transferAsset);
            console.log('   Base Asset Amount (DB amount):', baseAssetAmount);
            console.log('   Transfer Amount (DB transfer_amount):', transferDetails.transferAmount);
            console.log('   Counter Asset Amount (DB receivable_amount):', counterAssetAmount);
            console.log('📝 Inserting order into database:', JSON.stringify(orderData, null, 2));

            try {
            const dbResult = await this.insertData('stellar_orders', orderData);
                if (dbResult == false) {
                    console.error('❌ Database insert returned false/null');
                    return this.makeResponse(500, 'Failed to create order in database - insert returned false');
                }
                console.log('✅ Order inserted into database successfully');
            } catch (dbError: any) {
                console.error('❌ Database insert error:', dbError);
                console.error('Error details:', {
                    message: dbError.message,
                    code: dbError.code,
                    sqlMessage: dbError.sqlMessage,
                    sql: dbError.sql
                });
                return this.makeResponse(500, `Failed to create order in database: ${dbError.message || dbError}`);
            }

            // Step 9: Lock assets in trading account
            const walletResult = await this.makeWalletTransaction({
                debitClientId: clientId,
                creditClientId: "TRADER",
                amount: transferDetails.transferAmount,
                assetCode: transferDetails.transferAsset,
                assetIssuer: transferDetails.transferIssuer,
                productId: "TRADING_ORDER",
                memo: `TRADING_${orderType.toUpperCase()}_${orderId}`,
                transType: "TRADING_LOCK",
                serviceName: "TRADING"
            });

            if (walletResult.response !== 1) {
                return this.makeResponse(500, `Failed to lock assets: ${walletResult.message}`);
            }

            // Step 10: Activate order
            await this.updateData('stellar_orders', `order_id='${orderId}'`, {
                status: 'active',
                stellar_hash: walletResult.stellarHash
            });

            await this.logTradeAction(clientId, 'CREATE_OFFER', orderId, orderData);

            // Step 11: Try to auto-execute with matching orders
            const matchingResult = await this.checkAndExecuteMatchingOrders(orderId);

            if (matchingResult.hasMatches) {
                console.log(`✅ Auto-executed ${matchingResult.executedTrades} trades`);
                return this.makeResponse(200, 'Limit order created and partially/fully executed', {
                orderId,
                    stellarHash: walletResult.stellarHash,
                    autoExecuted: true,
                    executedTrades: matchingResult.executedTrades
                });
            }

            return this.makeResponse(200, 'Limit order created successfully', {
                orderId,
                stellarHash: walletResult.stellarHash,
                autoExecuted: false
            });

        } catch (error: any) {
            console.error('❌ Error creating offer:', error);
            await this.logTradeAction(data.clientId, 'CREATE_OFFER_ERROR', '', { error: error.message });

            // Return 400 for user errors (no liquidity, insufficient balance, etc.)
            // Return 500 for system errors (database failures, blockchain issues, etc.)
            const userErrors = [
                'No sell orders available',
                'No buy orders available',
                'Insufficient liquidity',
                'Insufficient balance',
                'No matching orders'
            ];

            const isUserError = userErrors.some(errMsg => error.message?.includes(errMsg));
            const statusCode = isUserError ? 400 : 500;
            const message = isUserError ? error.message : 'Failed to create offer';

            return this.makeResponse(statusCode, message, isUserError ? null : { error: error.message });
        }
    }

    /**
     * Find matching orders for a given source order
     * FIXED: Corrected price comparison logic for proper order matching
     */
    private async findMatchingOrders(sourceOrder: any, priceLimit?: number) {
        try {
            let matchingQuery = '';
            let matchingParams: any[] = [];

            if (sourceOrder.order_type === 'buy') {
                // BUY ORDER: Looking for SELL orders to match against
                // We want sell orders where the seller's price is <= our buy price
                // Price represents: counterAsset per baseAsset (e.g., UGX per USDT)
                matchingQuery = `
                    SELECT * FROM stellar_orders 
                    WHERE order_type = 'sell' 
                    AND selling_asset = ? 
                    AND buying_asset = ?
                    AND price <= ?
                    AND client_id != ?
                    AND status = 'active'
                    ORDER BY price ASC, created_at ASC
                    LIMIT 10
                `;
                matchingParams = [
                    sourceOrder.buying_asset,      // They're selling what we want to buy
                    sourceOrder.selling_asset,     // They want what we're selling
                    priceLimit || sourceOrder.price,
                    sourceOrder.client_id
                ];
            } else {
                // SELL ORDER: Looking for BUY orders to match against  
                // We want buy orders where the buyer's price is >= our sell price
                // Price represents: counterAsset per baseAsset (e.g., UGX per USDT)
                matchingQuery = `
                    SELECT * FROM stellar_orders 
                    WHERE order_type = 'buy' 
                    AND buying_asset = ? 
                    AND selling_asset = ?
                    AND price >= ?
                    AND client_id != ?
                    AND status = 'active'
                    ORDER BY price DESC, created_at ASC
                    LIMIT 10
                `;
                matchingParams = [
                    sourceOrder.selling_asset,     // They want to buy what we're selling
                    sourceOrder.buying_asset,      // They're selling what we want
                    priceLimit || sourceOrder.price,
                    sourceOrder.client_id
                ];
            }

            console.log(`🔍 Finding matching orders for ${sourceOrder.order_type} order:`, {
                query: matchingQuery,
                params: matchingParams
            });

            return await this.callQuerySafe(matchingQuery, matchingParams);
        } catch (error: any) {
            console.error('❌ Error finding matching orders:', error);
            return [];
        }
    }

    /**
     * Execute a single trade between two orders using direct batch transfer
     * Follows: DATABASE FIRST, BLOCKCHAIN SECOND, UPDATE THIRD
     */
    public async executeSingleTrade(takerOrder: any, makerOrder: any, tradeAmount: string, counterAmount: string, executionMode: string) {
        try {
            const takerClientId = takerOrder.client_id;
            const makerClientId = makerOrder.client_id;

            // Get taker's client info
            const takerApiInfo = await this.getDecryptedApiKey(takerClientId);
            if (!takerApiInfo) {
                return { success: false, message: 'Taker account not found' };
            }

            const makerApiInfo = await this.getDecryptedApiKey(makerClientId);
            if (!makerApiInfo) {
                return { success: false, message: 'Maker account not found' };
            }

            const tradeId = this.getTransId();

            // Get issuers for both assets (all assets use the same issuer from .env)
            const sellingIssuer = await this.GetTradingIssuer(makerOrder.selling_asset, takerClientId);
            const buyingIssuer = await this.GetTradingIssuer(makerOrder.buying_asset, takerClientId);

            // STEP 1: DATABASE FIRST - Insert trade record with empty hash
            await this.insertData('stellar_trades', {
                trade_id: tradeId,
                order_id: takerOrder.order_id || tradeId,
                maker_id: makerClientId,
                taker_id: takerClientId,
                amount: tradeAmount,
                price: makerOrder.price,
                stellar_hash: '', // Empty until blockchain execution
                created_at: formatDateForMySQL()
            });

            // Compose atomic transaction with all 3 transfers
            const recipients = [
                // 1. Taker pays counter-asset to TRADER
                {
                    publicKey: TRADING_ACCOUNT_PUBLIC,
                    amount: counterAmount,
                    asset_code: makerOrder.buying_asset,
                    asset_issuer: buyingIssuer,
                    senderSecretKey: takerApiInfo.secret_key,
                    creditPrivateKey: TRADING_ACCOUNT_SECRET
                },
                // 2. TRADER sends selling-asset to taker
                {
                    publicKey: takerApiInfo.public_key,
                    amount: tradeAmount,
                    asset_code: makerOrder.selling_asset,
                    asset_issuer: sellingIssuer,
                    senderSecretKey: TRADING_ACCOUNT_SECRET,
                    creditPrivateKey: takerApiInfo.secret_key
                },
                // 3. TRADER sends buying-asset to maker
                {
                    publicKey: makerApiInfo.public_key,
                    amount: counterAmount,
                    asset_code: makerOrder.buying_asset,
                    asset_issuer: buyingIssuer,
                    senderSecretKey: TRADING_ACCOUNT_SECRET,
                    creditPrivateKey: makerApiInfo.secret_key
                }
            ];

            // STEP 2: BLOCKCHAIN SECOND - Execute atomic transaction
            const result = await stellar.makeBatchTransfers(tradeId, 'TRADE', recipients);

            if (result.response !== 1) {
                // Mark trade as failed
                await this.updateData('stellar_trades', `trade_id='${tradeId}'`, {
                    stellar_hash: 'FAILED'
                });
                return { success: false, message: result.message };
            }

            const stellarHash = result.message;

            // STEP 3: UPDATE THIRD - Update with blockchain hash and order statuses
            await this.updateData('stellar_trades', `trade_id='${tradeId}'`, {
                stellar_hash: stellarHash
            });

            // Update maker order
            const newFilledAmount = parseFloat(makerOrder.filled_amount) + parseFloat(tradeAmount);
            await this.updateData('stellar_orders', `order_id='${makerOrder.order_id}'`, {
                filled_amount: newFilledAmount.toFixed(7),
                stellar_hash: stellarHash,
                status: newFilledAmount >= parseFloat(makerOrder.amount) ? 'filled' : 'partially_filled'
            });

            // Update taker order if it exists (for limit orders)
            if (takerOrder.order_id) {
                const takerNewFilledAmount = parseFloat(takerOrder.filled_amount || '0') + parseFloat(tradeAmount);
                await this.updateData('stellar_orders', `order_id='${takerOrder.order_id}'`, {
                    filled_amount: takerNewFilledAmount.toFixed(7),
                    stellar_hash: stellarHash,
                    status: takerNewFilledAmount >= parseFloat(takerOrder.amount) ? 'filled' : 'partially_filled'
                });
            }

            return {
                success: true,
                stellarHash: stellarHash,
                message: 'Trade executed successfully'
            };

        } catch (error: any) {
            console.error('❌ Error executing single trade:', error);
            return { success: false, message: error.message };
        }
    }

    /**
     * Check for matching orders and execute them automatically
     */
    private async checkAndExecuteMatchingOrders(newOrderId: string) {
        try {
            // Get the newly created order
            const newOrderResult: any = await this.callQuerySafe(
                `SELECT * FROM stellar_orders WHERE order_id = ? AND status = 'active' LIMIT 1`,
                [newOrderId]
            );

            if (!newOrderResult || newOrderResult.length === 0) {
                return { hasMatches: false, executedTrades: 0 };
            }

            const newOrder = newOrderResult[0];

            // Find matching orders
            const matchingOrders = await this.findMatchingOrders(newOrder);
            if (!matchingOrders || !Array.isArray(matchingOrders) || matchingOrders.length === 0) {
                return { hasMatches: false, executedTrades: 0 };
            }

            // Execute trades against matching orders
            let executedTrades = 0;
            let remainingAmount = parseFloat(newOrder.amount) - parseFloat(newOrder.filled_amount);

            for (const targetOrder of matchingOrders) {
                if (remainingAmount <= 0) break;

                // Skip own orders
                if (targetOrder.client_id.toString() === newOrder.client_id.toString()) {
                    continue;
                }

                const targetOrderRemaining = parseFloat(targetOrder.amount) - parseFloat(targetOrder.filled_amount);
                if (targetOrderRemaining <= 0) continue;

                // Calculate trade amount
                const tradeAmount = Math.min(remainingAmount, targetOrderRemaining);
                const counterAmount = tradeAmount * parseFloat(targetOrder.price);

                console.log(`📝 Auto-executing ${tradeAmount} against order ${targetOrder.order_id} @ ${targetOrder.price}`);

                // Execute the individual trade
                const tradeResult = await this.executeSingleTrade(
                    newOrder,
                    targetOrder,
                    tradeAmount.toString(),
                    counterAmount.toString(),
                    'AUTO'
                );

                if (tradeResult.success) {
                    executedTrades++;
                    remainingAmount -= tradeAmount;
                    console.log(`✅ Auto-trade executed: ${tradeAmount}, remaining: ${remainingAmount}`);
                } else {
                    console.error(`❌ Auto-trade failed: ${tradeResult.message}`);
                }
            }

            return {
                hasMatches: executedTrades > 0,
                executedTrades
            };

        } catch (error: any) {
            console.error('❌ Error checking matching orders:', error);
            return { hasMatches: false, executedTrades: 0 };
        }
    }

    public async getOrderBook(baseAsset: string, counterAsset: string, clientId: string) {
        try {
            console.log('🔹 Getting order book for:', { baseAsset, counterAsset });

            // If no specific pair requested, get all active orders
            let whereClause = `status IN ('active', 'partially_filled')`;

            if (baseAsset && counterAsset && baseAsset !== 'all' && counterAsset !== 'all') {
                // For a trading pair like USDT/UGX, we need to match:
                // - Buy orders: buying USDT with UGX (buying_asset=USDT, selling_asset=UGX)
                // - Sell orders: selling USDT for UGX (selling_asset=USDT, buying_asset=UGX)
                whereClause += ` AND (
                    (order_type='buy' AND buying_asset='${baseAsset}' AND selling_asset='${counterAsset}') 
                    OR 
                    (order_type='sell' AND selling_asset='${baseAsset}' AND buying_asset='${counterAsset}')
                )`;
            }

            const orders: any = await this.callRawQuery(`SELECT * FROM stellar_orders WHERE ${whereClause} ORDER BY created_at DESC LIMIT 100`);

            // Map database fields to frontend expected fields
            // For trading pairs, we need to standardize:
            // - Buy orders: buying USDT with UGX → base=USDT (asset you want), counter=UGX (what you pay)
            // - Sell orders: selling USDC for UGX → base=USDC (asset you sell), counter=UGX (what you get)
            // So base_asset is ALWAYS the crypto (USDT/USDC), counter_asset is ALWAYS the fiat (UGX)
            const mappedOrders = orders.map((order: any) => {
                // FIXED: order.amount is always the base asset amount
                // Calculate remaining base asset amount (for partially filled orders)
                const totalBaseAmount = parseFloat(order.amount);
                const filledBaseAmount = parseFloat(order.filled_amount || '0');
                const remainingBaseAmount = totalBaseAmount - filledBaseAmount;

                // Calculate remaining counter asset amount (what user can still receive)
                const totalCounterAmount = parseFloat(order.receivable_amount || (order.amount * order.price));
                const filledCounterAmount = filledBaseAmount * parseFloat(order.price);
                const remainingCounterAmount = totalCounterAmount - filledCounterAmount;

                return {
                    ...order,
                    base_asset: order.order_type === 'buy' ? order.buying_asset : order.selling_asset,
                    counter_asset: order.order_type === 'buy' ? order.selling_asset : order.buying_asset,
                    // Show remaining base asset amount (what's actually available to trade)
                    amount: remainingBaseAmount.toFixed(8),
                    // Show remaining counter asset amount (what user can still receive)
                    receivable_amount: remainingCounterAmount.toFixed(8),
                    // Keep original amounts for reference
                    original_amount: order.amount,
                    original_receivable_amount: order.receivable_amount || (order.amount * order.price),
                    filled_amount: order.filled_amount
                };
            });

            // Group orders by buy/sell
            const buyOrders = mappedOrders.filter((order: any) => order.order_type === 'buy');
            const sellOrders = mappedOrders.filter((order: any) => order.order_type === 'sell');

            // Sort buy orders: highest price first (top to bottom: best → worst)
            // Best buy orders (highest price) appear at top, closest to spread
            const sortedBuyOrders = buyOrders.sort((a: any, b: any) => parseFloat(b.price) - parseFloat(a.price));
            
            // Sort sell orders: lowest price first (bottom to top: best → worst)
            // Best sell orders (lowest price) appear at bottom, closest to spread
            const sortedSellOrders = sellOrders.sort((a: any, b: any) => parseFloat(a.price) - parseFloat(b.price));

            return this.makeResponse(200, 'Order book retrieved', {
                pair: baseAsset && counterAsset ? `${baseAsset}/${counterAsset}` : 'All Pairs',
                buyOrders: sortedBuyOrders,
                sellOrders: sortedSellOrders,
                totalOrders: orders.length,
                bestBid: sortedBuyOrders[0]?.price || null,
                bestAsk: sortedSellOrders[0]?.price || null,
                spread: sortedBuyOrders[0] && sortedSellOrders[0]
                    ? (parseFloat(sortedSellOrders[0].price) - parseFloat(sortedBuyOrders[0].price)).toFixed(8)
                    : null
            });

        } catch (error: any) {
            console.error('❌ Error getting order book:', error);
            return this.makeResponse(500, 'Failed to get order book', { error: error.message });
        }
    }





    /**
     * Cancel an existing order
     */
    public async cancelOrder(data: { clientId: string; orderId: string }) {
        try {
            console.log('🔹 Cancelling order:', data);

            const { clientId, orderId } = data;

            // Get the order
            const orders: any = await this.callQuerySafe(`SELECT * FROM stellar_orders WHERE order_id = ? AND client_id = ? AND status IN ('active', 'partially_filled') LIMIT 1`, [orderId, clientId]);
            if (!orders || orders.length === 0) {
                return this.makeResponse(400, 'Order not found or cannot be cancelled');
            }

            const order = orders[0];

            // Get client wallet info for cancellation
            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return this.makeResponse(400, 'Client account not found');
            }

            // Get the asset issuer (all assets use the same issuer from .env)
            const assetIssuer = await this.GetTradingIssuer(order.selling_asset, clientId);

            // Step 1: Update order status to 'cancelling' in database FIRST
            await this.updateData('stellar_orders', `order_id='${orderId}'`, {
                status: 'cancelling'
            });

            // Step 2: Return assets from central trading account to user using makeWalletTransaction
            const stellarResult = await this.makeWalletTransaction({
                debitClientId: "TRADER",
                creditClientId: clientId,
                amount: order.amount,
                assetCode: order.selling_asset,
                assetIssuer: assetIssuer,
                productId: "TRADING_CANCELLATION",
                memo: `TRADING_CANCEL_${orderId}`,
                transType: "TRADING_UNLOCK",
                serviceName: "TRADING"
            });

            // Step 3: Update order with final status
            if (stellarResult.response === 1) {
            await this.updateData('stellar_orders', `order_id='${orderId}'`, {
                status: 'cancelled'
            });

            // Log the cancellation
            await this.logTradeAction(clientId, 'CANCEL_ORDER', orderId.toString(), {
                originalOrder: order,
                    stellarHash: stellarResult.stellarHash
            });

            return this.makeResponse(200, 'Order cancelled successfully', {
                orderId,
                    stellarHash: stellarResult.stellarHash
                });
            } else {
                // Failed - revert to original status
                await this.updateData('stellar_orders', `order_id='${orderId}'`, {
                    status: order.status
                });

                return this.makeResponse(400, 'Failed to return assets', { error: stellarResult.message });
            }

        } catch (error: any) {
            console.error('❌ Error cancelling order:', error);
            await this.logTradeAction(data.clientId, 'CANCEL_ORDER_ERROR', '', { error: error.message });
            return this.makeResponse(500, 'Failed to cancel order', { error: error.message });
        }
    }

    /**
     * Get user's trading balance (assets held in central trading account)
     */
    public async getTradingBalance(clientId: string) {
        try {
            // Get all active and partially filled orders for this client
            const orders: any = await this.callQuerySafe(
                `SELECT * FROM stellar_orders 
                 WHERE client_id = ? AND status IN ('active', 'partially_filled')`,
                [clientId]
            );

            // Calculate locked balances by asset
            const lockedBalances: { [key: string]: number } = {};

            for (const order of orders) {
                const asset = order.selling_asset;
                // Use transfer_amount if available, otherwise calculate from amount
                const transferredAmount = order.transfer_amount
                    ? parseFloat(order.transfer_amount)
                    : parseFloat(order.amount);

                const filledRatio = parseFloat(order.filled_amount || '0') / parseFloat(order.amount);
                const lockedAmount = transferredAmount * (1 - filledRatio);

                if (!lockedBalances[asset]) {
                    lockedBalances[asset] = 0;
                }
                lockedBalances[asset] += lockedAmount;
            }

            // Convert to array format
            const balances = Object.entries(lockedBalances).map(([asset, amount]) => ({
                asset,
                locked: amount.toFixed(7),
                orders: orders.filter((o: any) => o.selling_asset === asset).length
            }));

            return this.makeResponse(200, 'Trading balance retrieved', {
                clientId,
                balances,
                totalOrders: orders.length
            });

        } catch (error: any) {
            console.error('❌ Error getting trading balance:', error);
            return this.makeResponse(500, 'Failed to get trading balance', { error: error.message });
        }
    }

    /**
     * Get price quote based on orderbook
     * Calculates how much you can get for a given amount based on available orders
     */
    public async getOrderbookPrice(data: {
        fromAsset: string;
        toAsset: string;
        amount: string;
        slippage?: string;
        clientId: string;
    }) {
        try {
            const { fromAsset, toAsset, amount, slippage = '0.01', clientId } = data;

            console.log('🔹 Getting orderbook price:', { fromAsset, toAsset, amount, slippage });

            // Get ALL orders (not filtered by pair)
            const orderBook = await this.getOrderBook(fromAsset, toAsset, clientId);

            if (orderBook.status !== 200) {
                return this.makeResponse(500, 'Failed to get orderbook');
            }

            // Find matching orders based on what we want to do:
            // If fromAsset=USDT, toAsset=UGX -> we want to SELL USDT for UGX
            //   Match with: BUY orders buying USDT with UGX (they're buying what we're selling)
            // If fromAsset=UGX, toAsset=USDT -> we want to SELL UGX for USDT  
            //   Match with: BUY orders buying UGX with USDT (they're buying what we're selling)
            
            const { buyOrders, sellOrders } = orderBook.data;

            // We're SELLING fromAsset FOR toAsset
            // So we need BUY orders that are BUYING fromAsset WITH toAsset
            let matchingOrders = buyOrders.filter((order: any) =>
                order.buying_asset === fromAsset && order.selling_asset === toAsset
            );

            console.log(`📊 Checking buy orders: Found ${matchingOrders.length} matching orders for selling ${fromAsset} for ${toAsset}`);

            // If no buy orders match, try sell orders
            // We can also match with SELL orders that are SELLING toAsset FOR fromAsset
            if (!matchingOrders || matchingOrders.length === 0) {
                matchingOrders = sellOrders.filter((order: any) =>
                    order.selling_asset === toAsset && order.buying_asset === fromAsset
                );
                
                console.log(`📊 Checking sell orders: Found ${matchingOrders.length} matching orders for ${fromAsset}/${toAsset}`);

                if (matchingOrders && matchingOrders.length > 0) {
                    // For sell orders, we need to invert the price
                    // If they're selling UGX for USDT at price X (USDT per UGX)
                    // And we want USDT price in UGX, we need 1/X
                    const bestOrder = matchingOrders[0];
                    const invertedPrice = 1 / parseFloat(bestOrder.price);
                    
                    return this.makeResponse(200, 'Price quote from sell orders (inverted)', {
                        fromAsset,
                        toAsset,
                        requestedAmount: amount,
                        averagePrice: invertedPrice.toString(),
                        price: invertedPrice.toString(),
                        bestPrice: invertedPrice.toString(),
                        ordersAvailable: matchingOrders.length
                    });
                }

                return this.makeResponse(404, `No orders available for ${fromAsset}/${toAsset} pair`);
            }

            // For buy orders, price is already correct
            const bestPrice = matchingOrders[0]?.price || '0';

            return this.makeResponse(200, 'Price quote from buy orders', {
                fromAsset,
                toAsset,
                requestedAmount: amount,
                averagePrice: bestPrice,
                price: bestPrice,
                bestPrice: bestPrice,
                ordersAvailable: matchingOrders.length
            });

        } catch (error: any) {
            console.error('❌ Error getting orderbook price:', error);
            return this.makeResponse(500, 'Failed to get price', { error: error.message });
        }
    }

    /**
     * Cancel all active orders for a client
     */
    public async cancelAllOrders(clientId: string) {
        try {
            console.log('🔹 Cancelling all orders for client:', clientId);

            // Get all active and partially filled orders
            const orders: any = await this.callQuerySafe(
                `SELECT * FROM stellar_orders 
                 WHERE client_id = ? AND status IN ('active', 'partially_filled')`,
                [clientId]
            );

            if (!orders || orders.length === 0) {
                return this.makeResponse(404, 'No active orders to cancel');
            }

            const cancelledOrders: any[] = [];
            const failedCancellations: any[] = [];

            console.log(`📊 Cancelling ${orders.length} orders...`);

            for (const order of orders) {
                const remainingAmount = (parseFloat(order.amount) - parseFloat(order.filled_amount || '0')).toString();

                if (parseFloat(remainingAmount) <= 0) {
                    console.log(`⏭️  Skipping fully filled order ${order.order_id}`);
                    continue;
                }

                console.log(`🔄 Cancelling order ${order.order_id}: ${remainingAmount} ${order.selling_asset}`);

                const cancelResult = await this.cancelOrder({
                    clientId,
                    orderId: order.order_id
                });

                if (cancelResult.status === 200) {
                    cancelledOrders.push({
                        orderId: order.order_id,
                        asset: order.selling_asset,
                        amount: remainingAmount,
                        hash: cancelResult.data?.stellarHash
                    });
                    console.log(`✅ Cancelled ${order.order_id}`);
                } else {
                    failedCancellations.push({
                        orderId: order.order_id,
                        error: cancelResult.message
                    });
                    console.log(`❌ Failed to cancel ${order.order_id}`);
                }
            }

            return this.makeResponse(200, 'Batch cancellation completed', {
                totalOrders: orders.length,
                cancelled: cancelledOrders.length,
                failed: failedCancellations.length,
                cancelledOrders,
                failedCancellations
            });

        } catch (error: any) {
            console.error('❌ Error cancelling all orders:', error);
            return this.makeResponse(500, 'Failed to cancel all orders', { error: error.message });
        }
    }

    /**
     * Get all available trading pairs from supported assets
     */
    public async getTradingPairs() {
        try {
            // Get all supported assets from database
            const stablecoins: any = await this.callRawQuery(
                `SELECT DISTINCT asset_code FROM supported_currencies 
                 WHERE asset_type = 'stableCoin' AND asset_code IS NOT NULL`
            );

            const fiatCurrencies: any = await this.callRawQuery(
                `SELECT DISTINCT asset_code FROM supported_currencies 
                 WHERE asset_type = 'fiat' AND asset_code IS NOT NULL`
            );

            const stablecoinCodes = stablecoins.map((row: any) => row.asset_code);
            const fiatCodes = fiatCurrencies.map((row: any) => row.asset_code);

            // Generate pairs: stablecoin/fiat combinations
            const pairs: any[] = [];

            // Stablecoin vs Fiat pairs
            stablecoinCodes.forEach((base: string) => {
                fiatCodes.forEach((counter: string) => {
                    pairs.push({
                        baseAsset: base,
                        counterAsset: counter,
                        pair: `${base}/${counter}`,
                        type: 'stablecoin_fiat'
                    });
                });
            });

            // Fiat vs Stablecoin pairs
            fiatCodes.forEach((base: string) => {
                stablecoinCodes.forEach((counter: string) => {
                    pairs.push({
                        baseAsset: base,
                        counterAsset: counter,
                        pair: `${base}/${counter}`,
                        type: 'fiat_stablecoin'
                    });
                });
            });

            // Fiat vs Fiat pairs (cross-fiat trading)
            fiatCodes.forEach((base: string) => {
                fiatCodes.forEach((counter: string) => {
                    if (base !== counter) {
                        pairs.push({
                            baseAsset: base,
                            counterAsset: counter,
                            pair: `${base}/${counter}`,
                            type: 'fiat_fiat'
                        });
                    }
                });
            });

            return this.makeResponse(200, 'Trading pairs retrieved', {
                pairs,
                totalPairs: pairs.length,
                stablecoins: stablecoinCodes,
                fiatCurrencies: fiatCodes
            });

        } catch (error: any) {
            console.error('❌ Error getting trading pairs:', error);
            return this.makeResponse(500, 'Failed to get trading pairs', { error: error.message });
        }
    }


    public async executeMarketSwapAndPayout(data: WebhookData) {
        try {
            console.log('🔄 Starting market swap and payout:', data);
            const { client_id, statusCode, reference_id } = data;
            const referenceId = reference_id;
            if (statusCode !== 200) {
                return this.makeResponse(statusCode, 'Failed to execute market swap and payout', { error: data.message });
            }


            const pendingOrder = await this.callQuerySafe(
                `SELECT * FROM quote_requests WHERE reference_id = ? AND status = 'pending' LIMIT 1`,
                [referenceId]
            );

            if (!pendingOrder || !Array.isArray(pendingOrder) || pendingOrder.length === 0) {
                return this.makeResponse(404, 'No pending rail order found for this reference', null);
            }

            const order = pendingOrder[0];
           
            await this.updateData('quote_requests', `reference_id='${referenceId}'`, {
                status: 'executing'
            });

            try {
               
                const orderData: TradeOffer = {
                    clientId: order.client_id,
                    baseAsset: order.source_asset,
                    counterAsset: order.destination_asset,
                    amount: order.amount.toString(),
                    orderType: 'buy',
                    type: 'market',
                    slippage: order.slippage,
                    referenceId: order.reference_id
                };

                const swapResult = await this.createOffer(orderData);

                if (swapResult.status !== 200) {
                    // Update status to failed
                    await this.updateData('quote_requests', `reference_id='${referenceId}'`, {
                        status: 'failed'
                    });

                    return this.makeResponse(swapResult.status, `Rail order execution failed: ${swapResult.message}`, swapResult.data);
                }

                // Update with execution details
                await this.updateData('quote_requests', `reference_id='${referenceId}'`, {
                    status: 'completed',
                    order_id: swapResult.data.orderId,
                    stellar_hash: swapResult.data.stellarHash
                });

                console.log('✅ Rail order executed successfully');

                // If payout data exists, execute payout
                if (order.payout_data) {
                    try {
                        const payoutData = JSON.parse(order.payout_data);
                        const payoutResult = await this.executeDirectPayout({
                            clientId: order.client_id,
                            amount: payoutData.amount,
                            currency: payoutData.currency,
                            mobileNumber: payoutData.mobileNumber,
                            provider: payoutData.provider,
                            memo: `Rail order payout - ${order.reference_id}`
                        });

                        if (payoutResult.status === 200) {
                            console.log('✅ Payout executed successfully');
                        } else {
                            console.error('❌ Payout failed:', payoutResult.message);
                        }
                    } catch (payoutError: any) {
                        console.error('❌ Error executing payout:', payoutError);
                    }
                }

                return this.makeResponse(200, 'Rail order executed successfully', {
                    requestId: order.request_id,
                    orderId: swapResult.data.orderId,
                    stellarHash: swapResult.data.stellarHash,
                    executedAmount: swapResult.data.executedTrades || 0
                });

            } catch (executionError: any) {
                // Update status to failed
                await this.updateData('quote_requests', `reference_id='${referenceId}'`, {
                    status: 'failed'
                });

                console.error('❌ Rail order execution failed:', executionError);
                return this.makeResponse(500, 'Rail order execution failed', { error: executionError.message });
            }

        } catch (error: any) {
            console.error('❌ Error executing pending rail order:', error);
            return this.makeResponse(500, 'Failed to execute pending rail order', { error: error.message });
        }
    }

    /**
     * Get rate quote for rail order (no address generation)
     */
    public async getRailOrderRate(data: {
        sourceAsset: string;
        destinationAsset: string;
        amount: string;
        slippage?: number;
    }): Promise<any> {
        try {
            console.log('💱 Getting rail order rate:', data);

            const { sourceAsset, destinationAsset, amount, slippage = 1 } = data;

            // Get market price from orderbook (use a dummy clientId for price lookup)
            const priceQuote = await this.getOrderbookPrice({
                fromAsset: sourceAsset,
                toAsset: destinationAsset,
                amount: amount,
                slippage: slippage.toString(),
                clientId: 'RATE_CHECK'
            });

            const marketPrice = priceQuote.status === 200 ? parseFloat(priceQuote.data.price) : 0;
            const estimatedReceiveAmount = parseFloat(amount) * marketPrice;

            return this.makeResponse(200, 'Rate quote calculated', {
                sourceAsset,
                destinationAsset,
                amount: parseFloat(amount),
                estimatedReceiveAmount,
                marketPrice,
                fee: 0,
                slippage
            });

        } catch (error: any) {
            console.error('❌ Error getting rail order rate:', error);
            return this.makeResponse(500, 'Failed to get rate', { error: error.message });
        }
    }

    /**
     * Create pending rail order with deposit address generation
     */
    public async createPendingRailOrder(data: {
        clientId: string;
        referenceId: string;
        sourceAsset: string;
        destinationAsset: string;
        amount: string;
        expectedAmount?: string;
        slippage?: number;
        payoutData?: any;
        expiresAt?: Date;
    }): Promise<any> {
        try {
            console.log('🚂 Creating pending rail order with deposit address:', data);

            const {
                clientId,
                referenceId,
                sourceAsset,
                destinationAsset,
                    amount,
                expectedAmount,
                slippage = 1,
                payoutData,
                expiresAt
            } = data;

            const requestId = this.getTransId();
            const expiresAtFormatted = expiresAt ? formatDateForMySQL(expiresAt) : null;

            // Get market price from orderbook
            const priceQuote = await this.getOrderbookPrice({
                fromAsset: sourceAsset,
                toAsset: destinationAsset,
                amount: amount,
                slippage: slippage.toString(),
                clientId: clientId
            });

            const marketPrice = priceQuote.status === 200 ? parseFloat(priceQuote.data.price) : 0;
            const estimatedReceiveAmount = parseFloat(amount) * marketPrice;

            // Call wallet service to generate deposit address
            const walletServiceUrl = process.env.WALLET_SERVICE_URL || 'http://localhost:8035';
            const depositEndpoint = `${walletServiceUrl}/payment/generate-address`;

            const depositData = {
                chain: 'evm',
                referenceId: referenceId,
                clientId: clientId,
                asset: sourceAsset,
                amount: parseFloat(amount)
            };

            console.log('📡 Calling wallet service for deposit address:', depositEndpoint);

            const response = await axios.post(depositEndpoint, depositData, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${process.env.INTERNAL_API_KEY || 'internal-key'}`
                }
            });

            const walletResult = response.data;

            if (walletResult.status !== 200) {
                console.error('❌ Wallet service error:', walletResult);
                return this.makeResponse(walletResult.status || 500, `Wallet service error: ${walletResult.message || 'Unknown error'}`, walletResult);
            }

            const depositAddress = walletResult.data?.deposit_address || walletResult.data?.address || walletResult.data;

            // Save to quote_requests table
            const quoteData = {
                request_id: requestId,
                client_id: clientId,
                reference_id: referenceId,
                source_asset: sourceAsset,
                destination_asset: destinationAsset,
                amount: parseFloat(amount),
                expected_amount: expectedAmount ? parseFloat(expectedAmount) : estimatedReceiveAmount,
                slippage: slippage,
                status: 'pending',
                payout_data: payoutData ? JSON.stringify(payoutData) : null,
                expires_at: expiresAtFormatted
            };

            const dbResult = await this.insertData('quote_requests', quoteData);

            if (dbResult === false) {
                return this.makeResponse(500, 'Failed to save pending rail order', null);
            }

            console.log('✅ Pending rail order created with deposit address');

            // Return response in format expected by Liquidity Rail Admin
            return this.makeResponse(200, 'Pending rail order created successfully', {
                id: requestId,
                fiatAmount: estimatedReceiveAmount,
                transactionAmount: parseFloat(amount),
                cryptoAmount: parseFloat(amount),
                fee: 0,
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
                value: marketPrice,
                address: depositAddress,
                referenceId: referenceId,
                network: 'stellar',
                asset: sourceAsset
            });

        } catch (error: any) {
            console.error('❌ Error creating pending rail order:', error);
            return this.makeResponse(500, 'Failed to create pending rail order', { error: error.message });
        }
    }



    /**
     * Get pending rail orders for a client
     */
    public async getPendingRailOrders(clientId: string) {
        try {
            const orders = await this.callQuerySafe(
                `SELECT * FROM quote_requests WHERE client_id = ? ORDER BY created_at DESC`,
                [clientId]
            );

            return this.makeResponse(200, 'Pending rail orders retrieved', { orders });

        } catch (error: any) {
            console.error('❌ Error getting pending rail orders:', error);
            return this.makeResponse(500, 'Failed to get pending rail orders', { error: error.message });
        }
    }

    /**
     * Execute auto market swap when deposit is received
     */
    public async executeAutoMarketSwap(data: {
        clientId: string;
        sourceAsset: string;
        destinationAsset: string;
        sourceAmount: string;
        slippage?: number;
    }) {
        try {
            console.log('🔄 Executing auto market swap:', data);

            const { clientId, sourceAsset, destinationAsset, sourceAmount, slippage = 1 } = data;

            // Create a market order for the auto swap
            const orderData: TradeOffer = {
                clientId,
                baseAsset: sourceAsset,
                counterAsset: destinationAsset,
                amount: sourceAmount,
                orderType: 'buy', // We're buying the destination asset with source asset
                type: 'market' as 'market' | 'limit',
                slippage
            };

            // Execute the market order
            const result = await this.createOffer(orderData);

            if (result.status !== 200) {
                console.error('❌ Auto market swap failed:', result.message);
                return result;
            }

            console.log('✅ Auto market swap completed successfully');
            return this.makeResponse(200, 'Auto market swap executed successfully', {
                swapId: result.data.orderId,
                sourceAsset,
                destinationAsset,
                sourceAmount,
                executedAmount: result.data.executedTrades || 0,
                stellarHash: result.data.stellarHash
            });

        } catch (error: any) {
            console.error('❌ Error in executeAutoMarketSwap:', error);
            return this.makeResponse(500, 'Auto market swap failed', { error: error.message });
        }
    }

    /**
     * Execute direct payout via wallet service
     */
    private async executeDirectPayout(data: {
        clientId: string;
        amount: string;
        currency: string;
        mobileNumber: string;
        provider: string;
        memo?: string;
    }) {
        try {
            const { clientId, amount, currency, mobileNumber, provider, memo = "Direct Payout" } = data;
            const walletServiceUrl = process.env.WALLET_SERVICE_URL || 'http://localhost:3005';
            const payoutEndpoint = `${walletServiceUrl}/direct-payout`;
            const payoutData = {
                "clientId": clientId,
                "reference_id": this.getTransId(),
                "amount": amount,
                "trans_type": "PUSH",
                "currency": currency,
                "product_id": 10011,
                "account_number": mobileNumber
            };

            console.log('📡 Calling wallet service direct payout:', payoutEndpoint);

            const response = await fetch(payoutEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${process.env.INTERNAL_API_KEY || 'internal-key'}`
                },
                body: JSON.stringify(payoutData)
            });

            const result = await response.json();

            if (!response.ok) {
                return this.makeResponse(response.status, `Wallet service error: ${result.message || 'Unknown error'}`, result);
            }

            return this.makeResponse(200, 'Direct payout executed successfully', result);

        } catch (error: any) {
            console.error('❌ Error in executeDirectPayout:', error);
            return this.makeResponse(500, 'Direct payout failed', { error: error.message });
        }
    }

    /**
     * Get trading history for a client
     */
    public async getTradingHistory(clientId?: string) {
        try {
            console.log('🔹 Getting trading history for client:', clientId);
            // If clientId is provided, filter by it; otherwise return all history
            let orders: any;
            let trades: any;
            let swaps: any;

            if (clientId) {
                orders = await this.callQuerySafe(`SELECT * FROM stellar_orders WHERE client_id = ? ORDER BY created_at DESC LIMIT 100`, [clientId]);
                trades = await this.callQuerySafe(`SELECT * FROM stellar_trades WHERE maker_id = ? OR taker_id = ? ORDER BY created_at DESC LIMIT 100`, [clientId, clientId]);
                swaps = await this.callQuerySafe(`SELECT * FROM stellar_swaps WHERE client_id = ? ORDER BY created_at DESC LIMIT 100`, [clientId]);
            } else {
                orders = await this.callRawQuery(`SELECT * FROM stellar_orders ORDER BY created_at DESC LIMIT 100`);
                trades = await this.callRawQuery(`SELECT * FROM stellar_trades ORDER BY created_at DESC LIMIT 100`);
                swaps = await this.callRawQuery(`SELECT * FROM stellar_swaps ORDER BY created_at DESC LIMIT 100`);
            }

            // Map orders to include base_asset and counter_asset for frontend consistency
            const mappedOrders = orders.map((order: any) => {
                // Calculate remaining amount (for partially filled orders)
                const totalAmount = parseFloat(order.amount);
                const filledAmount = parseFloat(order.filled_amount || '0');
                const remainingAmount = totalAmount - filledAmount;

                return {
                    ...order,
                    base_asset: order.order_type === 'buy' ? order.buying_asset : order.selling_asset,
                    counter_asset: order.order_type === 'buy' ? order.selling_asset : order.buying_asset,
                    // Keep original amount for "My Orders" view
                    original_amount: order.amount,
                    // Calculate remaining amount
                    remaining_amount: remainingAmount.toFixed(8)
                };
            });

            return this.makeResponse(200, 'Trading history retrieved', {
                orders: mappedOrders.sort((a: any, b: any) =>
                    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                ),
                trades: trades.sort((a: any, b: any) =>
                    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                ),
                swaps: swaps.sort((a: any, b: any) =>
                    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                )
            });

        } catch (error: any) {
            console.error('❌ Error getting trading history:', error);
            return this.makeResponse(500, 'Failed to get trading history', { error: error.message });
        }
    }

    /**
     * Get order by ID
     */
    public async getOrderById(orderId: string) {
        try {
            const order: any = await this.callQuerySafe(`SELECT * FROM stellar_orders WHERE order_id = ?`, [orderId]);

            if (order.length === 0) {
                return this.makeResponse(404, 'Order not found', null);
            }

            return this.makeResponse(200, 'Order retrieved successfully', order[0]);
        } catch (error: any) {
            console.error('❌ Error getting order by ID:', error);
            return this.makeResponse(500, 'Failed to get order', { error: error.message });
        }
    }

    async makeWalletTransaction(data: {
        debitClientId: string;
        creditClientId: string;
        amount: string;
        assetCode: string;
        assetIssuer: string;
        productId: string;
        memo?: string;
        transType?: string;
        serviceName?: string;
    }) {
        try {
            const {
                debitClientId,
                creditClientId,
                amount,
                assetCode,
                assetIssuer,
                productId,
                memo = "TRADING_TRANSFER",
                transType = "TRADING_TRANSFER",
                serviceName = "TRADING"
            } = data;

            const transId = this.getTransId();
            const SessionId = this.getTransId();

            // Get private keys for debit client
            let debitClientKeys;
            if (debitClientId === "TRADER") {
                debitClientKeys = {
                    public_key: TRADING_ACCOUNT_PUBLIC,
                    secret_key: TRADING_ACCOUNT_SECRET
                };
            } else {
                debitClientKeys = await this.getDecryptedApiKey(debitClientId);
                if (!debitClientKeys) {
                    return {
                        response: 0,
                        message: `Debit client ${debitClientId} not found`
                    };
                }
            }

            // Get private keys for credit client
            let creditClientKeys;
            let transactionType = "PUSH";
            if (creditClientId === "TRADER") {
                creditClientKeys = {
                    public_key: TRADING_ACCOUNT_PUBLIC,
                    secret_key: TRADING_ACCOUNT_SECRET
                };
            } else if (creditClientId === "TRADING_ACCOUNT") {
                creditClientKeys = {
                    public_key: TRADING_ACCOUNT_PUBLIC,
                    secret_key: TRADING_ACCOUNT_SECRET
                };
            } else {
                transactionType = "PULL";
                creditClientKeys = await this.getDecryptedApiKey(creditClientId);
                if (!creditClientKeys) {
                    return {
                        response: 0,
                        message: `Credit client ${creditClientId} not found`
                    };
                }
            }

            // Create batch transfer recipients
            const recipients = [{
                publicKey: creditClientKeys.public_key,
                amount: amount,
                asset_code: assetCode,
                asset_issuer: assetIssuer,
                senderSecretKey: debitClientKeys.secret_key,
                creditPrivateKey: creditClientKeys.secret_key
            }];

            // Execute batch transfer


            const walletTransactionData: TransactionInterfaceMini = {
                trans_id: transId,
                reference_id: transId,
                validation_id: this.getTransId(),
                product_id: productId,
                client_id: debitClientId,
                trans_type: transactionType,
                amount: amount,
                receiver_account: creditClientKeys.public_key,
                service_name: serviceName,
                sender_account: debitClientKeys.public_key,
                currency: assetCode,
                memo: memo
            };

            const walletResult = await this.handleTransactionCreated(walletTransactionData);

            const result = await stellar.makeBatchTransfers(transId, memo, recipients);

            if (result.response !== 1) {
                await new Model().updateTransaction(transId, "FAILED", result.message)
            }
            await new Model().updateTransaction(transId, "SUCCESS", result.message)
            console.log(`✅ Wallet transaction completed: ${transId} - ${amount} ${assetCode} from ${debitClientId} to ${creditClientId}`);

            return {
                response: 1,
                transactionId: transId,
                stellarHash: result.message,
                message: "Wallet transaction completed successfully"
            };

        } catch (error: any) {
            console.error('Error in makeWalletTransaction:', error);
            return {
                response: 0,
                transactionId: null,
                message: error.message
            };
        }
    }


   

    async handleTransactionCreated(data: TransactionInterfaceMini) {
        try {
            const product_id = data.product_id

            const { reference_id, validation_id, client_id, amount, currency } = data
            const mudaFee = 0
            const providerFee = 0
            const memo = data.memo || data.service_name
            const phone = data.sender_account || client_id.toString()
            const SessionId = this.getTransId()
            const trans_id = data.trans_id || this.getTransId()

            const refIdExists = await this.selectDataQuerySafe("transactions", { reference_id });
            if (refIdExists.length > 0) {
                return this.makeResponse(400, "Transaction already exists");
            }

            const transactionData: TransactionInterface = {
                reference_id: reference_id,
                validation_id: this.getTransId(),
                client_id: client_id.toString(),
                trans_type: data.trans_type,
                trans_id: trans_id,
                amount: amount.toString(),
                req_amount: amount.toString(),
                asset_code: currency,
                fee: mudaFee.toString(),
                provider_fees: providerFee.toString(),
                currency,
                receive_currency: currency,
                service_name: data.service_name,
                product_id: product_id,
                sender_account: phone,
                receiver_account: client_id.toString(),
                memo: memo,
                status: "PENDING",
                SessionId,
                payment_method_id: "-",
                running_balance: "0"
            };

            await this.insertData("transactions", transactionData);
            return this.makeResponse(200, "Transaction created successfully", data);
        } catch (error: any) {
            console.error("Error in webhookUtilia:", error);
            return this.makeResponse(500, "Transaction failed");
        }
    }
    /**
     * Log trade actions for audit trail
     */
    private async logTradeAction(clientId: string, action: string, orderId: string, data: any) {
        try {
            const logData = {
                client_id: clientId,
                action,
                order_id: orderId,
                data: JSON.stringify(data),
                ip_address: '0.0.0.0', // You can pass this from the controller
                created_at: formatDateForMySQL()
            };

            await this.insertData('trade_logs', logData);
        } catch (error) {
            console.error('⚠️ Failed to log trade action:', error);
        }
    }
}

export default StellarTrading; 
export { StellarTrading }; 