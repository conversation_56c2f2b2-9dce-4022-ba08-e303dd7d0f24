import StellarSdk, { Asset, Keypair, Operation, TransactionBuilder, Networks } from 'stellar-sdk';
import StellarService from '../helpers/StellarService';
import logger from '../utils/logger';
import Model from '../helpers/model';

interface TradeOffer {
    clientId: string;
    baseAsset: string;
    counterAsset: string;
    amount: string;
    price?: string;
    orderType: 'buy' | 'sell';
    type: 'market' | 'limit';
    slippage?: number;
    referenceId?: string;
    payoutData?: any;
}

interface ApiResponse {
    status: number;
    message: string;
    data: any;
}

interface PathPaymentResult extends ApiResponse {
    success: boolean;
    amount: string;
    price: string;
    stellarHash: string;
}

interface PriceResult extends ApiResponse {
    success: boolean;
    price: string;
}

interface OrderBookResult extends ApiResponse {
    success: boolean;
    buyOrders: any[];
    sellOrders: any[];
    bestBid?: string;
    bestAsk?: string;
    spread?: string;
}

class StellarSDEXTrading extends Model {
    private server: any;
    private networkPassphrase: string;

    constructor() {
        super();
        this.networkPassphrase = process.env.STELLAR_NETWORK_PASSPHRASE || Networks.TESTNET;
        this.server = new StellarSdk.Server(process.env.STELLAR_HORIZON_URL || 'https://horizon-testnet.stellar.org');
    }

    /**
     * Create an offer using Stellar's native SDEX
     * Handles both market and limit orders through Stellar's built-in matching
     */
    public async createOffer(data: TradeOffer) {
        try {
            logger.info('🔹 Creating SDEX offer:', data);

            const { clientId, baseAsset, counterAsset, amount, price, orderType, type } = data;

            // Get client wallet
            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return this.makeResponse(400, 'Client wallet not found');
            }

            const stellarService = new StellarService();

            if (type === 'market') {
                // Validate that price is provided for market orders
                if (!price) {
                    return this.makeResponse(400, 'Price is required for market orders');
                }

                // For market orders, use path payment for immediate execution
                const response = await stellarService.makePathPayment({
                    senderSecretKey: apiInfo.secret_key,
                    baseAsset,
                    counterAsset,
                    amount,
                    price, // Price from frontend
                    orderType,
                    slippage: data.slippage || 0.5
                });
                console.log('🔹 Market order response:', response);

                return this.makeResponse(200, 'Market order created successfully', response);

    /*
            // Get current market price for market orders
            let currentPrice = price!;
            if (type === 'market') {
                const priceResult = await this.getPrice(baseAsset, counterAsset, orderType);
                if (priceResult.success) {
                    currentPrice = priceResult.price;
                    console.log(`💰 Market price for ${orderType} ${baseAsset}/${counterAsset}: ${currentPrice}`);
            } else {
                    console.warn('⚠️ Could not get market price, using provided price:', price);
                }
            }

            */
        }else{
                // For limit orders, create a traditional offer
                const result = await stellarService.createLimitOffer({
                    senderSecretKey: apiInfo.secret_key,
                    baseAsset,
                    counterAsset,
                    amount,
                    price: price!,
                    orderType
                });

                if (result.response === 1) {
                    const stellarHash = result.message;
                    const stellarOfferId = result.data; // Get the actual offer ID from Stellar
                    
                    // Generate unique order_id
                    const orderId = `SDEX_${Date.now()}_${clientId}`;
                    
                    // Determine selling and buying assets based on order type
                    let sellingAsset, buyingAsset, sellingAmount, buyingAmount;
                    if (orderType === 'sell') {
                        // Selling base asset for counter asset
                        sellingAsset = baseAsset;
                        buyingAsset = counterAsset;
                        sellingAmount = amount;
                        buyingAmount = (parseFloat(amount) * parseFloat(price!)).toFixed(8);
                    } else {
                        // Buying base asset with counter asset
                        sellingAsset = counterAsset;
                        buyingAsset = baseAsset;
                        sellingAmount = (parseFloat(amount) * parseFloat(price!)).toFixed(8);
                        buyingAmount = amount;
                    }
                    
                    // Store order in database as a log
                    try {
                        const orderData = {
                            order_id: orderId,
                            client_id: clientId,
                            stellar_offer_id: stellarOfferId || stellarHash, // Store actual offer ID or hash as fallback
                            selling_asset: sellingAsset,
                            buying_asset: buyingAsset,
                            amount: parseFloat(amount),
                            transfer_amount: parseFloat(sellingAmount),
                            receivable_amount: parseFloat(buyingAmount), // Amount expected to receive
                            filled_amount: 0,
                            price: parseFloat(price!),
                            order_type: orderType,
                            status: 'active',
                            stellar_hash: stellarHash,
                            created_at: new Date(),
                            updated_at: new Date()
                        };
                        
                        console.log('💾 Storing order in database:', {
                            orderType,
                            amount: orderData.amount,
                            transfer_amount: orderData.transfer_amount,
                            receivable_amount: orderData.receivable_amount,
                            selling_asset: orderData.selling_asset,
                            buying_asset: orderData.buying_asset,
                            price: orderData.price
                        });
                        
                        await this.insertData('stellar_orders', orderData);
                        
                        logger.info(`✅ Order stored in database: ${orderId}, Stellar Offer ID: ${stellarOfferId}`);
                    } catch (dbError: any) {
                        logger.error('⚠️  Failed to store order in database:', dbError.message);
                        // Continue even if DB storage fails - order is on SDEX
                    }
                    
                // Update the stellar_offer_id from Horizon (async, don't block response)
                this.cacheOffers(apiInfo.public_key, clientId).catch(err => {
                    logger.warn('⚠️  Failed to cache offers:', err.message);
                });
            
                return this.makeResponse(200, 'Limit offer created successfully', result);
                } else {
                    return this.makeResponse(result.response === 203 ? 400 : 500, result.message);
                }
            }


        } catch (error: any) {
            logger.error('❌ Error creating SDEX offer:', error);
            return this.makeResponse(500, `Failed to create offer: ${error.message}`);
        }
    }

    /**
     * Execute a path payment for immediate execution (market orders)
     * Uses Stellar's built-in path finding to get the best price
     */
    public async makePathPayment(data: {
        clientId: string;
        baseAsset: string;
        counterAsset: string;
        amount: string;
        price: string;
        orderType: 'buy' | 'sell';
        slippage?: number;
    }): Promise<PathPaymentResult> {
        try {
            logger.info('🔄 Executing path payment via StellarService:', data);

            const apiInfo = await this.getDecryptedApiKey(data.clientId);
            if (!apiInfo) {
                return {
                    status: 400,
                    message: 'Client wallet not found',
                    data: null,
                    success: false,
                    amount: '0',
                    price: '0',
                    stellarHash: ''
                };
            }

            const stellarService = new StellarService();
            return await stellarService.makePathPayment({
                senderSecretKey: apiInfo.secret_key,
                baseAsset: data.baseAsset,
                counterAsset: data.counterAsset,
                amount: data.amount,
                price: data.price,
                orderType: data.orderType,
                slippage: data.slippage
            });

        } catch (error: any) {
            logger.error('❌ Error executing path payment:', error);
            return {
                status: 400,
                message: error.message,
                data: null,
                success: false,
                amount: '0',
                price: '0',
                stellarHash: ''
            };
        }
    }

    /**
     * Get current market price from Stellar's orderbook
     */
    public async getPrice(baseAsset: string, counterAsset: string, orderType: 'buy' | 'sell'): Promise<PriceResult> {
        try {
            logger.info('💰 Getting price for:', { baseAsset, counterAsset, orderType });

            const baseAssetStellar = await this.createStellarAsset(baseAsset);
            const counterAssetStellar = await this.createStellarAsset(counterAsset);

            // Get orderbook
            const orderbook = await this.server.orderbook(baseAssetStellar, counterAssetStellar).call();

            let price: string;

            if (orderType === 'sell') {
                // For SELL orders: Get best bid price (highest price buyers are willing to pay)
            if (!orderbook.bids || orderbook.bids.length === 0) {
                return { 
                    status: 400, 
                    message: 'No bids available', 
                    data: null,
                    success: false, 
                    price: '0' 
                };
            }
            const bestBid = orderbook.bids[0];
                price = bestBid.price;
            } else {
                // For BUY orders: Get best ask price (lowest price sellers are offering)
                if (!orderbook.asks || orderbook.asks.length === 0) {
                    return {
                        status: 400,
                        message: 'No asks available',
                        data: null,
                        success: false,
                        price: '0'
                    };
                }
                const bestAsk = orderbook.asks[0];
                price = bestAsk.price;
            }

            logger.info('✅ Current price:', { baseAsset, counterAsset, orderType, price });

            return {
                status: 200,
                message: 'Price retrieved successfully',
                data: { price },
                success: true,
                price: price
            };

        } catch (error: any) {
            logger.error('❌ Error getting price:', error);
            return { 
                status: 400, 
                message: error.message, 
                data: null,
                success: false, 
                price: '0' 
            };
        }
    }

    public async getOrderBook(baseAsset: string, counterAsset: string, clientId?: string): Promise<OrderBookResult> {
        try {
            // Default to USDT/UGX if 'all' is specified
            if (!baseAsset || baseAsset === 'all') {
                baseAsset = 'USDT';
            }
            if (!counterAsset || counterAsset === 'all') {
                counterAsset = 'UGX';
            }

            logger.info('📊 Getting SDEX orderbook for:', { baseAsset, counterAsset, clientId });

            const baseAssetStellar = await this.createStellarAsset(baseAsset, clientId);
            const counterAssetStellar = await this.createStellarAsset(counterAsset, clientId);

            // Get orderbook from Stellar
            const orderbook = await this.server.orderbook(baseAssetStellar, counterAssetStellar).call();

            // No database lookup needed - Stellar orderbook is self-contained
            // Note: Stellar orderbook doesn't include seller information, so we can't identify client orders

            // Map SDEX orders to match the old database format
            // Note: Stellar bids/asks are from the perspective of the orderbook (base/counter)
            // - Bids: Orders to BUY base (UGX) with counter (USDT)
            // - Asks: Orders to SELL base (UGX) for counter (USDT)
            // Stellar's price is always "price of what you're buying per what you're selling"
            // For bids: price = counter per base (e.g., USDT per UGX)
            // For asks: price = counter per base (e.g., USDT per UGX)
            console.log('orderbook====>', JSON.stringify(orderbook, null, 2));
            const allOrders = [
                ...orderbook.bids.map((bid: any) => {
                    // Bid: buying base (UGX), selling counter (USDT)
                    // Calculate price correctly from price_r (numerator/denominator)
const price = bid.price_r ? (bid.price_r.n / bid.price_r.d) : parseFloat(bid.price);
                    const normalizedPrice = price.toFixed(8);
                    
                    // Stellar orderbook doesn't include seller info, so all orders are marked as 'SDEX'
                    const actualClientId = 'SDEX';
                    const actualOrderId = `SDEX_BID_${bid.id}`;
                    
                    return {
                        order_id: actualOrderId,
                        stellar_offer_id: bid.id,
                        client_id: actualClientId,
                        base_asset: baseAsset,
                        counter_asset: counterAsset,
                        selling_asset: counterAsset,
                        buying_asset: baseAsset,
                        amount: (parseFloat(bid.amount) / parseFloat(normalizedPrice)).toFixed(7),
                        receivable_amount: (parseFloat(bid.amount) / parseFloat(normalizedPrice)).toFixed(7),
                        price: normalizedPrice,
                        order_type: 'buy',
                        status: 'active',
                        created_at: new Date().toISOString(),
                        filled_amount: '0',
                        original_amount: bid.amount,
                        original_receivable_amount: (parseFloat(bid.amount) * parseFloat(normalizedPrice)).toFixed(8)
                    };
                }),
                ...orderbook.asks.map((ask: any) => {
                    // Ask: selling base (UGX), buying counter (USDT)
                    // Calculate price correctly from price_r (numerator/denominator)
                    const price = ask.price_r ? (ask.price_r.n / ask.price_r.d) : parseFloat(ask.price);
                    const normalizedPrice = price.toFixed(8);
                    
                    // Stellar orderbook doesn't include seller info, so all orders are marked as 'SDEX'
                    const actualClientId = 'SDEX';
                    const actualOrderId = `SDEX_ASK_${ask.id}`;
                    
                    return {
                        order_id: actualOrderId,
                        stellar_offer_id: ask.id,
                        client_id: actualClientId,
                        base_asset: baseAsset,
                        counter_asset: counterAsset,
                        selling_asset: baseAsset,
                        buying_asset: counterAsset,
                        amount: ask.amount, // Base asset amount
                        receivable_amount: (parseFloat(ask.amount) * parseFloat(normalizedPrice)).toFixed(8),
                        price: normalizedPrice, // Counter per base (UGX per USDT)
                        order_type: 'sell',
                        status: 'active',
                        created_at: new Date().toISOString(),
                        filled_amount: '0',
                        original_amount: ask.amount,
                        original_receivable_amount: (parseFloat(ask.amount) * parseFloat(normalizedPrice)).toFixed(8)
                    };
                })
            ];

            // Separate buy and sell orders for frontend
            const buyOrders = allOrders.filter(order => order.order_type === 'buy');
            const sellOrders = allOrders.filter(order => order.order_type === 'sell');

            logger.info('✅ SDEX orderbook retrieved:', {
                totalOrders: allOrders.length,
                buyOrders: buyOrders.length,
                sellOrders: sellOrders.length
            });

            return {
                status: 200,
                message: 'Orders retrieved successfully',
                data: allOrders,
                success: true,
                buyOrders: buyOrders,
                sellOrders: sellOrders
            };

        } catch (error: any) {
            logger.error('❌ Error getting SDEX orderbook:', error);
            return {
                status: 400,
                message: error.message,
                data: null,
                success: false,
                buyOrders: [],
                sellOrders: []
            };
        }
    }

    /**
     * Cancel an order on Stellar SDEX
     * Works directly on-chain, no database lookup needed
     */
    public async cancelOrder(orderId: string, clientId: string) {
        try {
            logger.info('🚫 Cancelling SDEX order:', { orderId, clientId });

            // Get client wallet
            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return this.makeResponse(400, 'Client wallet not found');
            }

            const publicKey = apiInfo.public_key;
            logger.info('🔍 Fetching on-chain offers for:', publicKey);

            // Fetch all active offers from Stellar for this account
            const offersResponse = await this.server.offers()
                .forAccount(publicKey)
                .limit(200)
                .call();

            logger.info(`📋 Found ${offersResponse.records.length} active offers on-chain`);

            // Find the specific offer by ID
            const offer = offersResponse.records.find((o: any) => o.id === orderId);

            if (!offer) {
                logger.warn('❌ Offer not found on-chain:', orderId);
                return this.makeResponse(400, 'Order not found on Stellar network. It may have already been filled or cancelled.');
            }

            logger.info('✅ Found offer on-chain:', {
                id: offer.id,
                selling: offer.selling.asset_code || 'XLM',
                buying: offer.buying.asset_code || 'XLM',
                amount: offer.amount,
                price: offer.price
            });

            // Cancel the offer on Stellar by setting amount to 0
            const stellarService = new StellarService();
            const result = await stellarService.cancelOffer({
                senderSecretKey: apiInfo.secret_key,
                offerId: orderId,
                orderType: 'sell', // Type doesn't matter for cancellation
                baseAsset: offer.selling.asset_code || 'XLM',
                counterAsset: offer.buying.asset_code || 'XLM'
            });

            if (result.response === 1) {
                // Update order status in database if it exists
                try {
                    await this.callRawQuery(
                        `UPDATE stellar_orders SET status = 'cancelled', updated_at = NOW() WHERE order_id = ? OR stellar_offer_id = ?`,
                        [orderId, orderId]
                    );
                } catch (dbError) {
                    logger.warn('⚠️ Could not update database (order may not exist in DB):', dbError);
                }
                
                logger.info('✅ Order cancelled successfully on-chain:', orderId);
                return this.makeResponse(200, 'Order cancelled successfully', { orderId });
            } else {
                return this.makeResponse(result.response === 203 ? 400 : 500, result.message);
            }
        } catch (error: any) {
            logger.error('❌ Error cancelling order:', error);
            return this.makeResponse(500, `Failed to cancel order: ${error.message}`);
        }
    }

    /**
     * Cancel all active orders for a client
     * Delegates to StellarService for sponsored fee payment
     */
    public async cancelAllOrders(clientId: string) {
        try {
            const stellarService = new StellarService();
            const result = await stellarService.cancelAllOrdersByClientId(clientId);
            
            // Update database records to mark orders as cancelled if successful
            if (result.response === 1 && result.data?.offers) {
                const offerIds = result.data.offers.map((o: any) => o.offerId.toString());
            if (offerIds.length > 0) {
                const placeholders = offerIds.map(() => '?').join(',');
                await this.callRawQuery(
                    `UPDATE stellar_orders SET status = 'cancelled', updated_at = NOW() WHERE stellar_offer_id IN (${placeholders}) AND client_id = ?`,
                    [...offerIds, clientId]
                );
                }
            }
            
            return this.makeResponse(result.response === 1 ? 200 : result.response, result.message, result.data);
        } catch (error: any) {
            logger.error('❌ Error cancelling orders:', error);
            return this.makeResponse(500, `Failed to cancel orders: ${error.message}`);
        }
    }

    /**
     * Get order history for a client from Stellar
     * Database stores orders as logs, but we read from Stellar for real-time status
     */
    public async getOrderHistory(clientId: string, baseAsset?: string, counterAsset?: string) {
        try {
            logger.info('📜 Getting order history from Stellar:', { clientId, baseAsset, counterAsset });

            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return {
                    status: 400,
                    message: 'Client wallet not found',
                    data: []
                };
            }

            // Get account operations from Stellar for real-time data
            const operations = await this.server.operations()
                .forAccount(apiInfo.public_key)
                .order('desc')
                .limit(200)
                .call();

            // Filter for manage offer operations
            const offerOps = operations.records.filter((op: any) => 
                op.type === 'manage_sell_offer' || op.type === 'manage_buy_offer'
            );

            const orders = offerOps.map((op: any) => ({
                order_id: op.id,
                client_id: clientId,
                transaction_hash: op.transaction_hash,
                created_at: op.created_at,
                type: op.type,
                amount: op.amount,
                price: op.price,
                offer_id: op.offer_id
            }));

            logger.info('✅ Order history retrieved from Stellar:', { count: orders.length });

            return {
                status: 200,
                message: 'Order history retrieved successfully',
                data: orders
            };

        } catch (error: any) {
            logger.error('❌ Error getting order history from Stellar:', error);
            return {
                status: 400,
                message: error.message,
                data: []
            };
        }
    }

    /**
     * Get trade history for a client - returns orders, trades, and swaps
     */
    public async getTradeHistory(clientId: string, baseAsset?: string, counterAsset?: string) {
        try {
            logger.info('📈 Getting trade history:', { clientId, baseAsset, counterAsset });

            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return {
                    status: 400,
                    message: 'Client wallet not found',
                    data: {
                        orders: [],
                        trades: [],
                        swaps: []
                    }
                };
            }

            // Get active orders (offers) from Stellar
            const offers = await this.server.offers()
                .forAccount(apiInfo.public_key)
                .limit(200)
                .call();

            const activeOrders = offers.records.map((offer: any) => {
                const sellingAsset = offer.selling.asset_type === 'native' ? 'XLM' : offer.selling.asset_code;
                const buyingAsset = offer.buying.asset_type === 'native' ? 'XLM' : offer.buying.asset_code;
                const offerAmount = parseFloat(offer.amount || '0');
                const offerPrice = parseFloat(offer.price || '0');
                
                // Calculate what the user will receive
                const amountToReceive = offerAmount * offerPrice;
                
                // Determine base currency (USDC/USDT are base currencies)
                const isBaseCurrency = (asset: string) => ['USDC', 'USDT'].includes(asset);
                const isSellingBase = isBaseCurrency(sellingAsset);
                const isBuyingBase = isBaseCurrency(buyingAsset);
                
                // Determine order direction and amounts from user perspective
                let orderType: 'buy' | 'sell';
                let baseAsset: string;
                let counterAsset: string;
                let amountOffering: string;
                let amountWanting: string;
                let pricePerUnit: string;
                
                if (isSellingBase && !isBuyingBase) {
                    // Selling USDC/USDT for crypto = SELL USDC/USDT
                    orderType = 'sell';
                    baseAsset = sellingAsset; // The USDC/USDT you're selling
                    counterAsset = buyingAsset; // The crypto you're buying
                    amountOffering = offerAmount.toString(); // USDC/USDT amount
                    amountWanting = amountToReceive.toString(); // Crypto amount
                    pricePerUnit = offerPrice.toString();
                } else if (!isSellingBase && isBuyingBase) {
                    // Selling crypto for USDC/USDT = BUY USDC/USDT
                    orderType = 'buy';
                    baseAsset = buyingAsset; // The USDC/USDT you're buying
                    counterAsset = sellingAsset; // The crypto you're selling
                    amountOffering = offerAmount.toString(); // Crypto amount
                    amountWanting = amountToReceive.toString(); // USDC/USDT amount
                    pricePerUnit = offerPrice.toString();
                } else {
                    // Neither is base currency, use original logic
                    orderType = 'sell';
                    baseAsset = sellingAsset;
                    counterAsset = buyingAsset;
                    amountOffering = offerAmount.toString();
                    amountWanting = amountToReceive.toString();
                    pricePerUnit = offerPrice.toString();
                }
                
                return {
                    order_id: offer.id,
                    client_id: clientId,
                    base_asset: baseAsset,
                    counter_asset: counterAsset,
                    amount: amountOffering,
                    amount_wanting: amountWanting,
                    price: pricePerUnit,
                    order_type: orderType,
                    status: 'active',
                    created_at: offer.last_modified_time || new Date().toISOString(),
                    stellar_offer_id: offer.id,
                    // Additional fields for clarity
                    selling_asset: sellingAsset,
                    buying_asset: buyingAsset,
                    amount_offering: amountOffering,
                    amount_receiving: amountWanting,
                    filled_amount: '0', // Set to 0 for now since Stellar doesn't provide this directly
                    // Original Stellar offer data (fallback to current values for now)
                    original_amount: offerAmount.toString(),
                    original_price: offerPrice.toString(),
                    original_selling: sellingAsset,
                    original_buying: buyingAsset,
                    original_amount_to_receive: amountToReceive.toString()
                };
            });

            // Get completed trades from Stellar
            const trades = await this.server.trades()
                .forAccount(apiInfo.public_key)
                .order('desc')
                .limit(200)
                .call();

            const tradeHistory = trades.records.map((trade: any) => {
                // Calculate amounts safely
                const baseAmount = parseFloat(trade.base_amount || '0');
                const counterAmount = parseFloat(trade.counter_amount || '0');
                const price = trade.price ? (trade.price.n / trade.price.d) : 0;
                
                const baseAsset = trade.base_asset_type === 'native' ? 'XLM' : trade.base_asset_code;
                const counterAsset = trade.counter_asset_type === 'native' ? 'XLM' : trade.counter_asset_code;
                
                // Determine base currency (USDC/USDT are base currencies)
                const isBaseCurrency = (asset: string) => ['USDC', 'USDT'].includes(asset);
                const isBaseAsset = isBaseCurrency(baseAsset);
                const isCounterAsset = isBaseCurrency(counterAsset);
                
                // Determine the trading direction and amounts
                let orderType: 'buy' | 'sell';
                let amount: string;
                let received: string;
                let displayPrice: string;
                
                if (isBaseAsset && !isCounterAsset) {
                    // Base asset is USDC/USDT, counter is fiat/crypto
                    orderType = trade.base_is_seller ? 'sell' : 'buy';
                    amount = isNaN(baseAmount) ? '0' : baseAmount.toString();
                    received = isNaN(counterAmount) ? '0' : counterAmount.toString();
                    // Price should be USDC/USDT per crypto unit
                    displayPrice = isNaN(price) ? '0' : price.toString();
                } else if (!isBaseAsset && isCounterAsset) {
                    // Counter asset is USDC/USDT, base is fiat/crypto
                    orderType = trade.base_is_seller ? 'buy' : 'sell';
                    amount = isNaN(counterAmount) ? '0' : counterAmount.toString();
                    received = isNaN(baseAmount) ? '0' : baseAmount.toString();
                    // Price should be USDC/USDT per crypto unit (invert if needed)
                    displayPrice = isNaN(price) ? '0' : price.toString();
                } else {
                    // Neither is base currency, use original logic
                    orderType = trade.base_is_seller ? 'sell' : 'buy';
                    amount = isNaN(baseAmount) ? '0' : baseAmount.toString();
                    received = isNaN(counterAmount) ? '0' : counterAmount.toString();
                    displayPrice = isNaN(price) ? '0' : price.toString();
                }
                
                return {
                trade_id: trade.id,
                client_id: clientId,
                    base_asset: baseAsset,
                    counter_asset: counterAsset,
                    base_amount: isNaN(baseAmount) ? '0' : baseAmount.toString(),
                    counter_amount: isNaN(counterAmount) ? '0' : counterAmount.toString(),
                    amount: amount, // Amount traded
                    received: received, // Amount received
                    price: displayPrice, // Price from user's perspective
                    order_type: orderType, // Clear buy/sell direction
                created_at: trade.ledger_close_time,
                offer_id: trade.offer_id,
                    trade_type: orderType, // For compatibility
                    status: 'completed'
                };
            });

            logger.info('✅ Trade history retrieved:', {
                orders: activeOrders.length,
                trades: tradeHistory.length
            });

            return {
                status: 200,
                message: 'Trade history retrieved successfully',
                data: {
                    orders: activeOrders,
                    trades: tradeHistory,
                    swaps: [] // For now, swaps are handled separately
                }
            };

        } catch (error: any) {
            logger.error('❌ Error getting trade history:', error);
            return {
                status: 400,
                message: error.message,
                data: {
                    orders: [],
                    trades: [],
                    swaps: []
                }
            };
        }
    }

    /**
     * Get current price for a trading pair
     */
    private async getCurrentPrice(baseAsset: string, counterAsset: string, orderType: 'buy' | 'sell'): Promise<string> {
        const priceResult = await this.getPrice(baseAsset, counterAsset, orderType);
        return priceResult.success ? priceResult.price : '1';
    }

    /**
     * Attach a client to an existing order
     * This is useful when orders are created without client association
     */
    public async attachClientToOrder(orderId: string, clientId: string): Promise<ApiResponse> {
        try {
            logger.info('🔗 Attaching client to order:', { orderId, clientId });

            // Check if order exists
            const orderQuery = `SELECT * FROM stellar_orders WHERE order_id = ?`;
            const existingOrder = await this.callRawQuery(orderQuery, [orderId]);

            if (!existingOrder || existingOrder.length === 0) {
                return this.makeResponse(404, 'Order not found');
            }

            // Check if client exists
            const clientQuery = `SELECT * FROM trading_accounts WHERE client_id = ?`;
            const existingClient = await this.callRawQuery(clientQuery, [clientId]);

            if (!existingClient || existingClient.length === 0) {
                return this.makeResponse(404, 'Client not found');
            }

            // Update the order with the client ID
            const updateQuery = `UPDATE stellar_orders SET client_id = ?, updated_at = NOW() WHERE order_id = ?`;
            await this.callRawQuery(updateQuery, [clientId, orderId]);

            logger.info('✅ Client attached to order successfully');

            return this.makeResponse(200, 'Client attached to order successfully', {
                orderId,
                clientId,
                updated: true
            });

        } catch (error: any) {
            logger.error('❌ Error attaching client to order:', error);
            return this.makeResponse(500, `Failed to attach client: ${error.message}`);
        }
    }

    

    /**
     * Cache offers from Stellar into the database
     * This stores all active offers for an account in the database
     */
    public async cacheOffers(publicKey: string, clientId: string): Promise<ApiResponse> {
        try {
            logger.info('💾 Caching offers for account:', { publicKey, clientId });

            // Wait a bit for the offer to be available on Stellar
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Fetch all active offers for this account
            const offers = await this.server.offers().forAccount(publicKey).call();
            
            if (!offers.records || offers.records.length === 0) {
                logger.info('No offers found for account:', publicKey);
                return this.makeResponse(200, 'No offers to cache', { cachedCount: 0 });
            }

            logger.info(`📊 Found ${offers.records.length} offers to cache`);

            let cachedCount = 0;
            const errors: string[] = [];

            // Process each offer
            for (const offer of offers.records) {
                try {
                    // Extract asset information
                    const sellingAsset = offer.selling.asset_type === 'native' ? 'XLM' : offer.selling.asset_code;
                    const buyingAsset = offer.buying.asset_type === 'native' ? 'XLM' : offer.buying.asset_code;
                    
                    // Calculate amounts
                    const amount = parseFloat(offer.amount);
                    const price = parseFloat(offer.price);
                    const receivableAmount = amount * price;

                    // Determine order type based on what's being sold/bought
                    const orderType = sellingAsset === 'XLM' ? 'sell' : 'buy';

                    // Generate unique order ID
                    const orderId = `SDEX_${Date.now()}_${offer.id}`;

                    // Prepare order data
                    const orderData = {
                        order_id: orderId,
                        client_id: clientId,
                        stellar_offer_id: offer.id.toString(),
                        selling_asset: sellingAsset,
                        buying_asset: buyingAsset,
                        amount: amount,
                        transfer_amount: amount,
                        receivable_amount: receivableAmount,
                        filled_amount: 0,
                        price: price,
                        order_type: orderType,
                        status: 'active',
                        stellar_hash: null, // Not available from offers endpoint
                        created_at: new Date(),
                        updated_at: new Date()
                    };

                    // Check if offer already exists in database
                    const existingQuery = `SELECT id FROM stellar_orders WHERE stellar_offer_id = ?`;
                    const existing = await this.callRawQuery(existingQuery, [offer.id.toString()]);

                    if (existing && existing.length > 0) {
                        logger.info(`⏭️ Offer ${offer.id} already cached, skipping`);
                        continue;
                    }

                    // Insert the offer into database
                    await this.insertData('stellar_orders', orderData);
                    cachedCount++;

                    logger.info(`✅ Cached offer ${offer.id}: ${sellingAsset} → ${buyingAsset} (${amount} @ ${price})`);

                } catch (offerError: any) {
                    const errorMsg = `Failed to cache offer ${offer.id}: ${offerError.message}`;
                    logger.error('❌', errorMsg);
                    errors.push(errorMsg);
                }
            }

            logger.info(`✅ Successfully cached ${cachedCount} offers`);

            return this.makeResponse(200, `Cached ${cachedCount} offers successfully`, {
                totalOffers: offers.records.length,
                cachedCount: cachedCount,
                errors: errors.length > 0 ? errors : null
            });

        } catch (error: any) {
            logger.error('❌ Error caching offers:', error);
            return this.makeResponse(500, `Failed to cache offers: ${error.message}`);
        }
    }

    public async updateOfferIdByHash(publicKey: string): Promise<ApiResponse> {
        try {
            await new Promise(resolve => setTimeout(resolve, 1000));

            let offerId = null;
            try {
                const offers = await this.server.offers().forAccount(publicKey).call();
                // Find the offer we just created (latest one)
                const newOffer = offers.records.find((offer: any) => 
                    offer.selling && offer.buying
                );
                
                if (newOffer) {
                    offerId = newOffer.id;
                    console.log('📝 Offer ID:', offerId);
                }
            } catch (queryError: any) {
                console.error('⚠️ Could not retrieve offer ID:', queryError.message);
            }
            
            return {
                status: 200,
                message: 'Offer ID updated successfully',
                data: offerId
            };
        } catch (error: any) {
            return {
                status: 500,
                message: error.message,
                data: null
            };
        }
    }

    /**
     * Create Stellar Asset object
     */
    private async createStellarAsset(assetCode: string, clientId?: string): Promise<Asset> {
        if (assetCode === 'XLM') {
            return Asset.native();
        }
        
        // Use GetTradingIssuer to get the issuer
        const issuer = await this.GetTradingIssuer(assetCode, clientId || 'default');
        return new Asset(assetCode, issuer);
    }


}

export default StellarSDEXTrading;
