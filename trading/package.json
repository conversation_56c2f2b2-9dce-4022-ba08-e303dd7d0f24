{"name": "trading-service", "version": "1.2.4", "description": "", "main": "app.ts", "scripts": {"start": "NODE_OPTIONS=\"--max-old-space-size=4096\" ts-node src/app.ts", "test": "npm run test:health", "test:health": "jest tests/health.test.ts", "test:tembo": "jest tembo.integration.test.ts", "test:trading": "jest tests/trading-order.test.ts", "test:full-flow": "npx ts-node test-full-trading-flow.ts", "test:orderbook-all": "npx ts-node tests/test-orderbook-all.ts", "fund-test": "npx ts-node fund-test-account.ts", "dev": "NODE_OPTIONS=\"--max-old-space-size=4096\" nodemon --exec ts-node src/app.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"3": "^2.1.0", "@aws-sdk/client-cloudwatch-logs": "^3.450.0", "@prisma/client": "^6.11.1", "amqplib": "^0.10.3", "aws-sdk": "^2.1536.0", "axios": "^1.11.0", "bcryptjs": "^3.0.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "decimal.js": "^10.6.0", "dotenv": "^16.6.1", "ethers": "^6.15.0", "express": "^4.18.2", "express-fileupload": "^1.4.3", "express-rate-limit": "^8.0.1", "express-ws": "^5.0.2", "firebase-admin": "^12.5.0", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "mysql2": "^2.2.5", "node-cron": "^3.0.3", "node-rsa": "^1.1.1", "nodemailer": "^6.9.15", "prisma": "^6.11.1", "qrcode": "^1.5.4", "sequelize": "^6.35.2", "speakeasy": "^2.0.0", "stellar-sdk": "^7.0.0", "typeorm": "^0.3.25", "uuid": "^11.1.0", "xml2json": "^0.12.0", "zod": "^3.24.2"}, "devDependencies": {"@types/amqplib": "^0.10.1", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.4.4", "@types/express-ws": "^3.0.5", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.19.9", "@types/node-cron": "^3.0.11", "@types/node-rsa": "^1.1.4", "@types/nodemailer": "^6.4.14", "@types/qrcode": "^1.5.5", "@types/request": "^2.48.12", "@types/sequelize": "^4.28.19", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.7", "@types/xml2json": "^0.11.6", "i": "^0.3.7", "jest": "^30.0.5", "nodemon": "^3.1.10", "npm": "^10.4.0", "redis": "^4.7.1", "supertest": "^7.1.4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}