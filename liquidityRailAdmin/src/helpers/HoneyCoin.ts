import RequestHelper from "../helpers/request.helper";
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import Model from './model';
import BaseModel from "./base.model";

// Types and interfaces
interface HoneyCoinConfig {
  apiUrl: string;
  cryptoApiUrl: string;
  publicKey: string;
  apiKey: string;
}

interface TokenResponse {
  status: "success" | "error";
  token?: string;
  message?: string;
}

interface QuoteInfo {
  provider: string;
  providerQuoteId: string;
  from: string;
  providerId: number;
  to: string;
  fiatAmount: number;
  toAmount: number;
  cryptoAmount: number;
  fee: number;
  quoteId: string;
  expiresAt: string;
  quotedPrice: string;
}

interface CollectionPayload {
  amount: number;
  phoneNumber: string;
  currency: string;
  externalReference: string;
  momoOperatorId?: string;
  walletCurrency?: string;
}

interface PayoutPayload {
  amount: number;
  currency: string;
  country: string;
  externalReference: string;
  payoutMethod: {
    accountName: string;
    accountNumber: string;
    code: string;
    branchCode: string;
  };
  destination: string;
}

interface OffRampPayload {
  senderAmount: number;
  senderCurrency: string;
  receiverCurrency: string;
  chain: string;
  email: string;
  country: string;
  destination: string;
  payoutMethod: {
    country: string;
    destination: string;
    accountNumber: string;
    accountName: string;
  };
  externalReference: string;
}


interface OnRampPayload {
  chargeDetails: {
    momoOperatorId: string;
    phoneNumber: string;
    email: string;
  };
  senderAmount: number;
  senderCurrency: string;
  receiverCurrency: string;
  chain: string; 
  email: string;
  receiverAddress: string;
  method: string;
  externalReference: string;
}


interface Bank {
  id: number;
  bank_code: string;
  name: string;
  country: string;
  country_code: string;
  currency: string;
  status: string;
  createdAt: string | null;
  updatedAt: string | null;
  deletedAt: string | null;
  data: any;
}

class HoneyCoin extends BaseModel {
  private config: HoneyCoinConfig;
  private requestHeaders: Record<string, string>;

  constructor() {
    super();
    this.config = {
      apiUrl: process.env.HONEYCOIN_API_URL ?? "",
      cryptoApiUrl: process.env.HONEYCOIN_CRYPTO_API_URL ?? "",
      publicKey: process.env.HONEYCOIN_PUBLIC_KEY ?? "",
      apiKey: process.env.HONEYCOIN_API_KEY ?? ""
    };

    this.requestHeaders = {
      "Accept": "application/json",
      "Content-Type": "application/json"
    };
  }

  // Generate authentication token for HoneyCoin API
  private async generateToken(): Promise<TokenResponse> {
    try {
      const endpoint = "/auth/generate-bearer-token";
      const data = {
        publicKey: this.config.publicKey,
        "api-key": this.config.apiKey,
      };

      axios.defaults.headers.common['api-key'] = this.config.apiKey;

      const response: AxiosResponse = await axios.post(
        `${this.config.apiUrl}${endpoint}`,
        data
      );

      if (response?.data?.success) {
        return {
          status: "success",
          token: response.data.token
        };
      }

      return {
        status: "error",
        message: response?.data?.message || "Failed to generate token"
      };
    } catch (error: any) {
      console.error("Token generation error:", error);
      return {
        status: "error",
        message: error?.message ?? "Error generating token"
      };
    }
  }

  // Make request to HoneyCoin API
  private async makeRequest(
    method: "get" | "post" | "put",
    endpoint: string,
    data: any = {},
    useCryptoApi: boolean = false
  ): Promise<any> {
    try {
      const baseUrl = useCryptoApi ? this.config.cryptoApiUrl : this.config.apiUrl;
      const fullUrl = `${baseUrl}${endpoint}`.replace(/\s/g, "");

      console.log(`makeRequest ------ > fullUrl `, fullUrl, data)
      await RequestHelper.setEndpoint(fullUrl);
      const tokenResponse = await this.generateToken();
      if (tokenResponse.status !== "success" || !tokenResponse.token) {
        throw new Error("Failed to generate Honeycoin token");
      }

      this.requestHeaders["Authorization"] = `Bearer ${tokenResponse.token}`;
      await RequestHelper.setHeaders(this.requestHeaders);

      if (data && Object.keys(data).length > 0) {
        await RequestHelper.setData(data);
      }

      await RequestHelper[`${method}Request`]();
      const errors = await RequestHelper.getErrors();
      console.log("errors", errors)
      const responseData = await RequestHelper.getResults();
      console.log("responseData", responseData)
      return responseData;

    } catch (error: any) {
      console.error(`Request error for ${method.toUpperCase()} ${endpoint}:`, error);
      throw new Error(error.message || "Request failed");
    }
  }

  //Generate unique reference for transactions
  private generateUniqueReference(): string {
    return `MUDA_PAYMENT${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
  }

  //Get list of supported countries
  public async getCountries(): Promise<any> {
    return await this.makeRequest("get", "/utilities/countries");
  }



  // Get list of banks for a specific country
  public async getBanks(country: string, currency: string): Promise<any> {
    const response = await this.makeRequest("get", `/utilities/banks?country=${country}`);
    if (!response?.data?.success) {
      return null;
    }
    let data = response.data.data;
    data = data.map((item: any): Bank => ({
      id: item?.id,
      bank_code: item?.code,
      name: item?.name,
      country: "",
      country_code: country,
      currency: currency || "",
      status: "",
      createdAt: "",
      updatedAt: "",
      deletedAt: "",
      data: ""
    }));
    return data;
  }

  /**
   * Get bank branches for a specific bank
   */
  public async getBankBranch(bankId: string): Promise<any> {
    const response = await this.makeRequest("get", `/utilities/branches?bankId=${bankId}`);
    if (!response?.data?.success) {
      return null;
    }
    const data = response.data.data;
    return data;
  }

  // Get mobile money providers for a currency
  public async getMobileMoneyProviders(currency: string = ""): Promise<any> {
    return await this.makeRequest("get", `/utilities/momo/providers?currency=${currency}`);
  }

  // Get all users
  public async getUsers(): Promise<any> {
    return await this.makeRequest("get", "/users");
  }

  // Get all transactions
  public async getTransactions(): Promise<any> {
    const response = await this.makeRequest("get", "/transactions");
    return response.data;
  }

  // Get a specific transaction by ID
  public async getTransaction(transactionId: string): Promise<any> {
    const response = await this.makeRequest("get", `/transactions/${transactionId}`);
    return response.data;
  }

  // Create a mobile money collection
  public async createCollection(
    amount: number,
    phoneNumber: string,
    currency: string,
    externalReference: string,
    momoOperatorId: string = ""
  ): Promise<any> {
    const payload: CollectionPayload = {
      amount,
      phoneNumber,
      currency,
      externalReference,
      momoOperatorId
    };
    const response = await this.makeRequest("post", "/fiat/deposit/momo", payload);
    return response;
  }

  // Verify collection with OTP
  public async verifyCollection(transactionId: string): Promise<any> {
    return await this.makeRequest("post", `/fiat/deposit/${transactionId}/validate-otp`);
  }

  // Create a payout transaction
  public async createPayout(
    amount: number,
    currency: string,
    country: string,
    externalReference: string,
    accountName: string,
    accountNumber: string,
    code: string,
    destination: string,
    branchCode: string
  ): Promise<any> {
    const payoutMethod: any = {
      accountName,
      accountNumber
    };
    if (destination === "Bank Account") {
      payoutMethod.branchCode = branchCode;
    }
    if (currency !== "KES" && code !== "") {
      payoutMethod.code = code;
    }
    const payload: PayoutPayload = {
      amount,
      currency,
      country,
      externalReference,
      payoutMethod,
      destination
    };
    const response = await this.makeRequest("post", "/fiat/payout", payload);
    return response.data;
  }


  // Get foreign exchange rate
  public async getFXRate(
    fromCurrency: string,
    toCurrency: string = "UGX",
    amount: number
  ): Promise<any> {
    return await this.makeRequest(
      "get",
      `/utilities/rates?from=${fromCurrency}&to=${toCurrency}&amount=${amount}`
    );
  }

  // Get off-ramp exchange rate
  public async getOffRampRate(
    fromCurrency: string,
    toCurrency: string,
    amount: number
  ): Promise<QuoteInfo | null> {
    try {
      console.log(`getOffRampRate`, fromCurrency, toCurrency, amount);
      const response = await this.makeRequest(
        "get",
        `/utilities/rates?from=${fromCurrency}&to=${toCurrency}&amount=${amount}`
      );
      console.log(`getOffRampRate ------ > resoinse `, response.data)
      if (!response?.data?.success) {
        return null;
      }
      const data = response.data.data;

      const providerQuoteId = data?.id || await this.uniqueReference()
      return {
        provider: "honeycoin",
        providerQuoteId: providerQuoteId,
        from: fromCurrency,
        providerId: 6,
        to: toCurrency,
        fiatAmount: data?.convertedAmount || 0,
        toAmount: data?.convertedAmount || 0,
        cryptoAmount: data?.cryptoAmount ?? amount,
        fee: data?.fee || 0,
        quoteId: data?.id || "",
        expiresAt: "",
        quotedPrice: data?.conversionRate || ""
      };
    } catch (error) {
      console.error("Error getting off-ramp rate:", error);
      return null;
    }
  }

  // Execute off-ramp transaction
  public async offRamp(
    senderAmount: number,
    senderCurrency: string,
    receiverCurrency: string = "KES",
    chain: string,
    email: string,
    country: string,
    destination: string,
    externalReference: string,
    payoutMtdCountry: string,
    payoutMtdProviderId: string,
    payoutMtdAccountNumber: string,
    payoutMtdAccountName: string,
    otherDetails: any = {}
  ): Promise<any> {


    // ETH, ARB, BASE, MATIC, BSC, and OPTIMISM
    if (payoutMtdProviderId === "mobile") {
      destination = "MoMo";
    } else if (payoutMtdProviderId === "bank") {
      destination = "Bank Account";
    }

    const paymentMethodDetails: any = {}
    paymentMethodDetails.country = payoutMtdCountry
    paymentMethodDetails.destination = destination
    paymentMethodDetails.accountNumber = payoutMtdAccountNumber
    paymentMethodDetails.accountName = payoutMtdAccountName
    if (payoutMtdProviderId === "bank" && otherDetails?.bank_code !== undefined && otherDetails?.bank_code !== "") {
      paymentMethodDetails.code = otherDetails?.bank_code || ""
    }

    const payload: OffRampPayload = {
      senderAmount,
      senderCurrency,
      receiverCurrency,
      chain,
      email,
      country,
      destination,
      payoutMethod: paymentMethodDetails,
      externalReference: externalReference || this.generateUniqueReference()
    };
    console.log(`HoneyCoin payload`, payload)
    this.LogOperation(externalReference, 'HONEY_COIN', 'OFFRAMP_REQUEST', payload);
    const response = await this.makeRequest("post", "/minting/offramp", payload, true);
    console.log(`HoneyCoin response`, response.data)
    this.LogOperation(externalReference, 'HONEY_COIN', 'OFFRAMP_RESPONSE', response.data);
    return response?.data;
  }


  public async onRamp(
    senderAmount: number,
    senderCurrency: string,
    receiverCurrency: string,
    chain: string,
    email: string,
    momoOperatorId: string,
    destination: string,
    externalReference: string,
    payoutMtdProviderId: string,
    payoutMtdAccountNumber: string,
    payoutMtdAccountName: string,
    receiverAddress: string,
    otherDetails: any = {}
  ): Promise<any> {

    let paymentMethodDetails: any = {};
    if (payoutMtdProviderId === "mobile_money") {
      destination = "momo";
      paymentMethodDetails  = {
                                momoOperatorId: momoOperatorId,
                                phoneNumber: payoutMtdAccountNumber,
                                email: email
                              };
    } else if (payoutMtdProviderId === "bank") {
      destination = "bank";
      paymentMethodDetails  = {
                                email: email
                              };
    }
    
    const payload: OnRampPayload = {
      chargeDetails: paymentMethodDetails,
      senderAmount: senderAmount,
      senderCurrency: senderCurrency,
      receiverCurrency: receiverCurrency,
      chain: chain,
      email: email,
      receiverAddress: receiverAddress,
      method: destination,
      externalReference: externalReference || this.generateUniqueReference()
    };

    console.log(`HoneyCoin payload`, payload);
    this.LogOperation(externalReference, 'HONEY_COIN', 'ONRAMP_REQUEST', payload);
    const response = await this.makeRequest("post", "/minting/onramp", payload, true);
    console.log(`HoneyCoin response`, response);
    this.LogOperation(externalReference, 'HONEY_COIN', 'ONRAMP_RESPONSE', response.data || response);
    return response?.data || response;
  }


  // {
  //   country: payoutMtdCountry,
  //   destination: destination,
  //   accountNumber: payoutMtdAccountNumber,
  //   accountName: payoutMtdAccountName
  // }
  public async LogOperation(quote_id: string, provider: string, action: string, data: any) {
    await this.insertData('quote_audit_log', {
      quote_id,
      provider,
      action,
      data: typeof data === 'object' ? JSON.stringify(data) : data
    })
  }

  // Simulate payment webhook
  public async simulatePaymentWebhook(webhookStatus: string): Promise<any> {
    return await this.makeRequest("post", "/utilities/simulate-webhook", { webhookStatus });
  }

  // Get all webhooks
  public async getWebhooks(): Promise<any> {
    return await this.makeRequest("get", "/webhooks");
  }

  // Get specific webhook by ID
  public async getWebhook(webhookId: string): Promise<any> {
    return await this.makeRequest("get", `/webhook/${webhookId}`);
  }

  // Resend webhook by ID
  public async resendWebhook(webhookId: string): Promise<any> {
    return await this.makeRequest("post", `/fiat/payoutwebhooks/${webhookId}/resend`);
  }

  public async uniqueReference() {
    return `MUDA_PAYMENT${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
  }

  // validate phone number 
  public async validatePhoneNumber(
    currency: string,
    country: string,
    phoneNumber: string
  ): Promise<any> {

    const payload = {
      currency,
      country,
      phoneNumber
    };
    const response: any = await this.makeRequest("post", `/utilities/operators/phone`, payload, false);
    
    const responseData = (response?.data !== undefined) ? response?.data  : response;
    console.log("response  validate phone number  ____ ", response, responseData);
    return responseData;

  }


  // refund a deposit 
  public async refundDeposit(
    externalReference: string,
    refundReason: string,
    transactionId: string
  ): Promise<any> {
    const payload = {
      refundReason,
      externalReference
    };
    const response = await this.makeRequest("post", `/fiat/deposit/${transactionId}/refund`, payload, false);
    console.log("response  refund deposit", response);
    return response?.data;
  }


  // accepted onramp chanins 
  public async getAcceptedOnRampChains(chain: string): Promise<any> {
     
    const array : string[] = ["ETH", "ARB", "BASE", "MATIC", "BSC", "OPTIMISM"]
     if(array.includes(chain)){
      return true
     }
     return false
  }


  // accespted on ramp assets
  // USDC and USDT
  public async getAcceptedOnRampAssets(asset: string): Promise<any> {
   
     const array : string[] = ["USDC", "USDT"]
     if(array.includes(asset)){
       return true
     }
     return false
  }
  
  public async validatePayoutPhoneNumber(
    currency: string,
    country: string,
    phoneNumber: string
  ): Promise<any> {

    try{
      const validatePhoneNumber = await this.validatePhoneNumber(currency, country, phoneNumber);
      if (!validatePhoneNumber?.success) {
        return {
            status : false,
            message : validatePhoneNumber.message,
            data : validatePhoneNumber?.data || "" }
      }
      const contactDetails : any = validatePhoneNumber.data.filter((opt: any) => opt === "mpesa");
      if (contactDetails.length === 0) {
        return {
          status : false,
          message : validatePhoneNumber.message || `Mpesa payouts  not supported for ${phoneNumber} `,
          data : validatePhoneNumber?.data || "" }
      }

      return {
        status : true  }
    } catch(e: any) {
      return {
        status : false, 
        message: "Error valdating account number"  }}    
  }


  public async validatePayinPhoneNumber(
    currency: string,
    country: string,
    phoneNumber: string
  ): Promise<any> {

    try{
      const validatePhoneNumber = await this.validatePhoneNumber(currency, country, phoneNumber);
      if (!validatePhoneNumber?.success) {
        return {
            status : false,
            message : validatePhoneNumber.message,
            data : validatePhoneNumber?.data || "" }
      }
      
      return {
        status : true  
      }
    } catch(e: any) {
      return {
        status : false, 
        message: "Error valdating account number"  }}    
  }
  
}

export default HoneyCoin;
