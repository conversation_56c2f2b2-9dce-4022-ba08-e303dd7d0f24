import Model from "../helpers/model";
import { SaveQuoteRequest } from "../interfaces/quote.interfaces";

// Route step types
interface RouteStep {
    step_index: number;
    step_type: 'swap' | 'bridge' | 'payout';
    provider: string;
    from_token: string;
    to_token: string;
    from_chain: string;
    to_chain: string;
    estimated_amount: number;
    estimated_fee: number;
    provider_quote_id?: string;
}

interface UniversalRoute {
    route_type: 'direct' | 'multi_step' | 'special_cngn_swap';
    total_steps: number;
    steps: RouteStep[];
    total_fees: number;
    final_amount: number;
    estimated_duration_minutes: number;
}

// Provider route templates - based on existing provider capabilities
interface ProviderTemplate {
    name: string;
    direct_support: string[];
    preferred_intermediate: string;
    output_currencies: string[];
    special_handling: { [key: string]: any };
}

const PROVIDER_ROUTE_TEMPLATES: { [key: number]: ProviderTemplate } = {
    1: { // Quidax
        name: 'Quidax',
        direct_support: ['USDT@TRON', 'USDT@BSC', 'USDC@ETH'],
        preferred_intermediate: 'USDT@BSC',
        output_currencies: ['NGN'],
        special_handling: {
            'CNGN': {
                // CNGN must be swapped to supported token via Quidax swap API first
                force_provider: true,
                swap_to_token: 'USDT', // Swap CNGN to USDT via Quidax
                intermediate_currency: 'USDT'
            }
        }
    },
    2: { // Muda
        name: 'Muda',
        direct_support: [], // Currently no direct crypto support
        preferred_intermediate: 'USDT@BSC',
        output_currencies: ['UGX'],
        special_handling: {}
    },
    6: { // HoneyCoin
        name: 'HoneyCoin',
        direct_support: [
            'USDC@ETH', 'USDC@ARB', 'USDC@BASE', 'USDC@MATIC', 'USDC@BSC', 'USDC@OPTIMISM',
            'USDT@ETH', 'USDT@ARB', 'USDT@BASE', 'USDT@MATIC', 'USDT@BSC', 'USDT@OPTIMISM'
        ],
        preferred_intermediate: 'USDC@BSC',
        output_currencies: ['KES'],
        special_handling: {}
    }
};

class UniversalOfframpService extends Model {
    constructor() {
        super();
    }

    /**
     * Main entry point - determines if route is direct or multi-step
     */
    async generateUniversalQuote(data: SaveQuoteRequest): Promise<any> {
        try {
            const { asset_code, chain, receive_currency, send_amount } = data;

            // Validate required fields
            if (!asset_code || !chain || !receive_currency) {
                return this.makeResponse(400, 'Missing required fields: asset_code, chain, receive_currency');
            }

            console.log(`🌍 Universal Off-ramp Request:`, {
                from: `${asset_code}@${chain}`,
                to: receive_currency,
                amount: send_amount
            });

            // Find suitable provider for the output currency
            const targetProvider = await this.findProviderForCurrency(receive_currency);
            if (!targetProvider) {
                return this.makeResponse(400, `No provider found for currency: ${receive_currency}`);
            }

            console.log(`🎯 Target provider: ${targetProvider.name} (ID: ${targetProvider.id})`);

            // Check for special handling (e.g., CNGN)
            const specialHandling = this.checkSpecialHandling(asset_code, targetProvider.id);
            if (specialHandling) {
                return await this.handleSpecialRoute(data, specialHandling, targetProvider);
            }

            // Check if direct route is possible
            const routeType = await this.detectRouteType(asset_code, chain, targetProvider.id);

            if (routeType === 'direct') {
                console.log(`✅ Direct route available`);
                return await this.generateDirectQuote(data, targetProvider.id);
            } else {
                console.log(`🔄 Multi-step route required`);
                return await this.generateMultiStepQuote(data, targetProvider);
            }

        } catch (error: any) {
            console.error('❌ Universal off-ramp error:', error);
            return this.makeResponse(500, 'Internal error generating universal quote');
        }
    }

    /**
     * Check if provider directly supports the token/chain combination
     */
    private async detectRouteType(asset_code: string, chain: string, provider_id: number): Promise<'direct' | 'multi_step'> {
        const template = PROVIDER_ROUTE_TEMPLATES[provider_id as keyof typeof PROVIDER_ROUTE_TEMPLATES];
        if (!template) return 'multi_step';

        const inputKey = `${asset_code}@${chain}`;
        return template.direct_support.includes(inputKey) ? 'direct' : 'multi_step';
    }

    /**
     * Find provider that supports the target currency
     */
    private async findProviderForCurrency(currency: string): Promise<any> {
        for (const [providerId, template] of Object.entries(PROVIDER_ROUTE_TEMPLATES)) {
            if (template.output_currencies.includes(currency)) {
                return {
                    id: parseInt(providerId),
                    name: template.name,
                    template: template
                };
            }
        }
        return null;
    }

    /**
     * Check for special token handling (e.g., CNGN)
     */
    private checkSpecialHandling(asset_code: string, provider_id: number): any {
        const template = PROVIDER_ROUTE_TEMPLATES[provider_id as keyof typeof PROVIDER_ROUTE_TEMPLATES];
        return template?.special_handling?.[asset_code] || null;
    }

    /**
     * Handle special routes like CNGN → NGN → UGX
     */
    private async handleSpecialRoute(data: SaveQuoteRequest, specialHandling: any, targetProvider: any): Promise<any> {
        console.log(`🔥 Special handling for ${data.asset_code}:`, specialHandling);
        
        if (data.asset_code === 'CNGN' && specialHandling.force_provider) {
            // CNGN must first be swapped to supported token via Quidax
            return await this.generateCNGNSwapRoute(data, targetProvider, specialHandling);
        }

        return this.makeResponse(400, 'Special handling not implemented for this token');
    }

    /**
     * Generate CNGN → USDT → Target Currency route (using Quidax swap)
     */
    private async generateCNGNSwapRoute(data: SaveQuoteRequest, targetProvider: any, specialHandling: any): Promise<any> {
        const steps: RouteStep[] = [];

        console.log(`🔄 Generating CNGN swap route: CNGN → ${specialHandling.swap_to_token} → ${data.receive_currency}`);

        // Step 1: CNGN → USDT (Quidax swap)
        const quidaxSwapQuote = await this.getQuidaxSwapQuote(
            data.send_amount,
            'CNGN',
            specialHandling.swap_to_token || 'USDT'
        );

        if (!quidaxSwapQuote.success) {
            return this.makeResponse(400, `Failed to get Quidax CNGN swap quote: ${quidaxSwapQuote.message}`);
        }

        steps.push({
            step_index: 1,
            step_type: 'swap',
            provider: 'Quidax',
            from_token: 'CNGN',
            to_token: specialHandling.swap_to_token || 'USDT',
            from_chain: data.chain || 'CELO',
            to_chain: 'BSC', // USDT typically on BSC for better compatibility
            estimated_amount: quidaxSwapQuote.data.receive_amount,
            estimated_fee: quidaxSwapQuote.data.fee,
            provider_quote_id: quidaxSwapQuote.data.quotation_id
        });

        // Step 2: USDT → Target Currency (via appropriate provider)
        const swappedToken = specialHandling.swap_to_token || 'USDT';
        const swappedChain = 'BSC';

        const targetQuote = await this.getProviderQuote(
            quidaxSwapQuote.data.receive_amount,
            swappedToken,
            data.receive_currency,
            targetProvider.id
        );

        if (!targetQuote.success) {
            return this.makeResponse(400, `Failed to get target currency quote: ${targetQuote.message}`);
        }

        steps.push({
            step_index: 2,
            step_type: 'payout',
            provider: targetProvider.name,
            from_token: swappedToken,
            to_token: data.receive_currency,
            from_chain: swappedChain,
            to_chain: 'FIAT',
            estimated_amount: targetQuote.data.receive_amount,
            estimated_fee: targetQuote.data.fee,
            provider_quote_id: targetQuote.data.provider_quote_id
        });

        const route: UniversalRoute = {
            route_type: 'special_cngn_swap',
            total_steps: steps.length,
            steps: steps,
            total_fees: steps.reduce((sum, step) => sum + step.estimated_fee, 0),
            final_amount: steps[steps.length - 1].estimated_amount,
            estimated_duration_minutes: steps.length * 8 // 8 minutes per step for swaps
        };

        console.log(`✅ CNGN swap route generated: ${steps.length} steps, total fees: ${route.total_fees}`);
        return await this.saveUniversalQuote(data, route);
    }

    /**
     * Generate direct quote using existing provider logic
     */
    private async generateDirectQuote(data: SaveQuoteRequest, provider_id: number): Promise<any> {
        // Use existing saveQuote logic for direct routes
        const modifiedData = { ...data, provider_id: provider_id.toString() };
        
        // Import the existing accounts model to use saveQuote
        const AccountsModel = require('../models/accounts').default;
        const accounts = new AccountsModel();
        
        return await accounts.saveQuote(modifiedData);
    }

    /**
     * Generate multi-step quote with Rhino bridge/swap + provider payout
     */
    private async generateMultiStepQuote(data: SaveQuoteRequest, targetProvider: any): Promise<any> {
        const template = targetProvider.template;
        const [intermediateToken, intermediateChain] = template.preferred_intermediate.split('@');
        
        const steps: RouteStep[] = [];

        // Step 1: User Token → Intermediate Token (Rhino)
        const rhinoQuote = await this.getRhinoSwapQuote(
            data.asset_code!,
            data.chain!,
            intermediateToken,
            intermediateChain,
            data.send_amount
        );

        if (!rhinoQuote.success) {
            return this.makeResponse(400, 'Failed to get Rhino swap quote');
        }

        steps.push({
            step_index: 1,
            step_type: 'swap',
            provider: 'Rhino',
            from_token: data.asset_code!,
            to_token: intermediateToken,
            from_chain: data.chain!,
            to_chain: intermediateChain,
            estimated_amount: rhinoQuote.data.receive_amount,
            estimated_fee: rhinoQuote.data.fee,
            provider_quote_id: rhinoQuote.data.quote_id
        });

        // Step 2: Intermediate Token → Fiat (Provider)
        const providerQuote = await this.getProviderQuote(
            rhinoQuote.data.receive_amount,
            intermediateToken,
            data.receive_currency,
            targetProvider.id
        );

        if (!providerQuote.success) {
            return this.makeResponse(400, `Failed to get ${targetProvider.name} payout quote`);
        }

        steps.push({
            step_index: 2,
            step_type: 'payout',
            provider: targetProvider.name,
            from_token: intermediateToken,
            to_token: data.receive_currency,
            from_chain: intermediateChain,
            to_chain: 'fiat',
            estimated_amount: providerQuote.data.receive_amount,
            estimated_fee: providerQuote.data.fee,
            provider_quote_id: providerQuote.data.provider_quote_id
        });

        const route: UniversalRoute = {
            route_type: 'multi_step',
            total_steps: steps.length,
            steps: steps,
            total_fees: steps.reduce((sum, step) => sum + step.estimated_fee, 0),
            final_amount: steps[steps.length - 1].estimated_amount,
            estimated_duration_minutes: steps.length * 30
        };

        return await this.saveUniversalQuote(data, route);
    }

    /**
     * Get CNGN → USDT swap quote from Quidax
     */
    private async getQuidaxSwapQuote(amount: number, fromCurrency: string, toCurrency: string): Promise<any> {
        try {
            console.log(`🔄 Getting Quidax swap quote: ${amount} ${fromCurrency} → ${toCurrency}`);

            // Import existing Quidax integration
            const Quidax = require('../helpers/Quidax').default;
            const quidax = new Quidax();

            // Use Quidax swap quotation API
            const swapQuote = await quidax.makeCryptoFiatSwap('me', fromCurrency, toCurrency, amount);

            console.log('Quidax swap quote response:', swapQuote);

            if (swapQuote && swapQuote.success && swapQuote.data) {
                const quoteData = swapQuote.data;
                return {
                    success: true,
                    data: {
                        quotation_id: quoteData.id,
                        receive_amount: parseFloat(quoteData.to_amount || quoteData.toAmount || '0'),
                        fee: parseFloat(quoteData.fee || '0'),
                        rate: parseFloat(quoteData.rate || '0'),
                        expires_at: quoteData.expires_at
                    }
                };
            } else {
                return {
                    success: false,
                    message: swapQuote?.message || 'Failed to get Quidax swap quote'
                };
            }
        } catch (error: any) {
            console.error('Error getting Quidax swap quote:', error);
            return { success: false, message: error.message || 'Unknown error' };
        }
    }

    /**
     * Get CNGN → NGN quote from Quidax (legacy method - kept for compatibility)
     */
    private async getQuidaxCNGNQuote(amount: number): Promise<any> {
        try {
            // Import existing Quidax integration
            const AccountsModel = require('../models/accounts').default;
            const accounts = new AccountsModel();

            // Use existing Quidax quote method
            const quidaxQuote = await accounts.getQuidaxQuote('CNGN', 'NGN', amount);

            if (quidaxQuote && quidaxQuote.providerQuoteId) {
                return {
                    success: true,
                    data: {
                        receive_amount: quidaxQuote.toAmount || amount * 0.98,
                        fee: quidaxQuote.fee || amount * 0.02,
                        provider_quote_id: quidaxQuote.providerQuoteId
                    }
                };
            } else {
                return { success: false, message: 'Failed to get Quidax CNGN quote' };
            }
        } catch (error: any) {
            console.error('Error getting Quidax CNGN quote:', error);
            return { success: false, message: error.message || 'Unknown error' };
        }
    }

    /**
     * Get quote for target currency conversion
     */
    private async getTargetCurrencyQuote(amount: number, from: string, to: string, provider_id: number): Promise<any> {
        try {
            const AccountsModel = require('../models/accounts').default;
            const accounts = new AccountsModel();

            // Use existing provider quote methods based on provider_id
            let providerQuote;

            switch (provider_id) {
                case 6: // HoneyCoin
                    providerQuote = await accounts.getHoneyCoinQuote(from, to, amount);
                    break;
                case 2: // Muda
                    providerQuote = await accounts.getMudaQuote(from, to, amount);
                    break;
                default:
                    throw new Error(`Unsupported provider ID: ${provider_id}`);
            }

            if (providerQuote && providerQuote.providerQuoteId) {
                return {
                    success: true,
                    data: {
                        receive_amount: providerQuote.toAmount || amount * 0.97,
                        fee: providerQuote.fee || amount * 0.03,
                        provider_quote_id: providerQuote.providerQuoteId
                    }
                };
            } else {
                return { success: false, message: 'Failed to get provider quote' };
            }
        } catch (error: any) {
            console.error('Error getting target currency quote:', error);
            return { success: false, message: error.message || 'Unknown error' };
        }
    }

    /**
     * Get Rhino swap/bridge quote
     */
    private async getRhinoSwapQuote(fromToken: string, fromChain: string, toToken: string, toChain: string, amount: number): Promise<any> {
        try {
            // Import Rhino client
            const rhinoClient = require('../helpers/Rhino').default;

            const rhinoRequest = {
                fromChain: fromChain,
                toChain: toChain,
                fromToken: fromToken,
                toToken: toToken,
                amount: amount.toString(),
                recipient: await this.getMudaAddress(toChain) // Use our internal address as recipient
            };

            const rhinoResponse = await rhinoClient.getQuote(rhinoRequest);

            if (rhinoResponse.success) {
                const rhinoQuote = rhinoResponse.data;
                const totalFees = rhinoQuote.fees?.reduce((sum: number, fee: any) => sum + parseFloat(fee.amount || '0'), 0) || 0;

                return {
                    success: true,
                    data: {
                        receive_amount: rhinoQuote.outputAmount,
                        fee: totalFees,
                        quote_id: rhinoQuote.id
                    }
                };
            } else {
                return { success: false, message: rhinoResponse.message };
            }
        } catch (error: any) {
            console.error('Error getting Rhino quote:', error);
            return { success: false, message: error.message || 'Unknown error' };
        }
    }

    /**
     * Get provider quote for final payout
     */
    private async getProviderQuote(amount: number, token: string, currency: string, provider_id: number): Promise<any> {
        try {
            const AccountsModel = require('../models/accounts').default;
            const accounts = new AccountsModel();

            // Use existing provider rate methods
            let providerQuote;

            switch (provider_id) {
                case 1: // Quidax
                    providerQuote = await accounts.getQuidaxQuote(token, currency, amount);
                    break;
                case 6: // HoneyCoin
                    providerQuote = await accounts.getHoneyCoinQuote(token, currency, amount);
                    break;
                case 2: // Muda
                    providerQuote = await accounts.getMudaQuote(token, currency, amount);
                    break;
                default:
                    throw new Error(`Unsupported provider ID: ${provider_id}`);
            }

            if (providerQuote && providerQuote.providerQuoteId) {
                return {
                    success: true,
                    data: {
                        receive_amount: providerQuote.toAmount || amount * 0.98,
                        fee: providerQuote.fee || amount * 0.02,
                        provider_quote_id: providerQuote.providerQuoteId
                    }
                };
            } else {
                return { success: false, message: 'Failed to get provider quote' };
            }
        } catch (error: any) {
            console.error('Error getting provider quote:', error);
            return { success: false, message: error.message || 'Unknown error' };
        }
    }

    /**
     * Save universal quote to database with route information
     */
    private async saveUniversalQuote(data: SaveQuoteRequest, route: UniversalRoute): Promise<any> {
        try {
            const transId = this.getRandomString();

            // Save main quote record
            const quoteData = {
                transId: transId,
                company_id: data.company_id,
                client_reference_id: data.reference_id || `REF${Date.now()}`,
                send_asset: data.asset_code,
                send_amount: data.send_amount,
                receive_currency: data.receive_currency,
                receive_amount: route.final_amount,
                fee: route.total_fees,
                status: 'QUOTE_GENERATED',
                pay_in_status: 'PENDING',
                transaction_type: 'off_ramp',
                route_type: 'multi_step', // Mark as universal route
                created_on: new Date(),
                expires_at: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
            };

            await this.insertData('quotes', quoteData);

            // Save route information
            const routeData = {
                quote_id: transId,
                route_type: route.route_type,
                total_steps: route.total_steps,
                current_step: 0,
                completed_steps: 0,
                failed_steps: 0,
                overall_status: 'pending',
                total_fees: route.total_fees,
                final_amount: route.final_amount,
                estimated_duration_minutes: route.estimated_duration_minutes
            };

            await this.insertData('universal_routes', routeData);

            // Save individual steps
            for (const step of route.steps) {
                const stepData = {
                    quote_id: transId,
                    step_index: step.step_index,
                    step_type: step.step_type,
                    provider: step.provider,
                    from_token: step.from_token,
                    to_token: step.to_token,
                    from_chain: step.from_chain,
                    to_chain: step.to_chain,
                    estimated_amount: step.estimated_amount,
                    estimated_fee: step.estimated_fee,
                    status: 'pending',
                    provider_quote_id: step.provider_quote_id
                };

                await this.insertData('route_steps', stepData);
            }

            console.log(`✅ Universal quote saved: ${transId} with ${route.total_steps} steps`);

            return this.makeResponse(200, 'Universal quote generated successfully', {
                quote_id: transId,
                reference_id: data.reference_id,
                status: 'QUOTE_GENERATED',
                route_type: route.route_type,
                total_steps: route.total_steps,
                total_fees: route.total_fees,
                final_amount: route.final_amount,
                estimated_duration_minutes: route.estimated_duration_minutes,
                steps: route.steps.map(step => ({
                    step_index: step.step_index,
                    step_type: step.step_type,
                    provider: step.provider,
                    from: `${step.from_token}@${step.from_chain}`,
                    to: `${step.to_token}@${step.to_chain}`,
                    estimated_amount: step.estimated_amount,
                    estimated_fee: step.estimated_fee
                })),
                expires_at: quoteData.expires_at
            });

        } catch (error: any) {
            console.error('Error saving universal quote:', error);
            return this.makeResponse(500, 'Failed to save universal quote');
        }
    }

    /**
     * Get Muda address for a specific chain
     */
    async getMudaAddress(chain: string): Promise<string> {
        try {
            const result = await this.callQuerySafe(
                'SELECT address FROM provider_addresses WHERE provider_id = 2 AND chain = ? AND status = "active" LIMIT 1',
                [chain]
            );

            if (Array.isArray(result) && result.length > 0) {
                return result[0].address;
            }

            throw new Error(`No active Muda address found for chain: ${chain}`);
        } catch (error: any) {
            console.error('Error getting Muda address:', error);
            throw error;
        }
    }
}

export default UniversalOfframpService;
