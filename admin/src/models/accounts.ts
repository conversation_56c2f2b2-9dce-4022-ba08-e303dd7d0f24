import Model from "../helpers/model";
import StellarSdk from "stellar-sdk";
import { v4 as uuidv4 } from "uuid";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>elper from "../helpers/2fa.helper";
import CryptoJS from "crypto-js";
import jwt from "jsonwebtoken";
import StellarService from "../helpers/StellarService";
import RatesService from "../helpers/exchangerates.helper";
import e from "express";
import dotenv from 'dotenv';
import { systemProductCodes } from "../helpers/interface";
import WalletService from "../services/wallet.service";
import { post } from "../helpers/httpRequest";
dotenv.config();
const ratesService = new RatesService();

const SECRET_KEY = process.env.SECRET_KEY || "";

import { setItem } from "../helpers/connectRedis";

class Accounts extends Model {

  constructor() {
    super();
  }

  async getUserAddresses(clientId: string) {
    const addresses = await this.selectDataQuerySafe("whitelisted_addresses", { client_id: clientId });
    return this.makeResponse(200, "Wallet addresses fetched successfully", addresses);
  }

  async getUserWhitelist(clientId: string) {
    const whitelist = await this.selectDataQuerySafe("whitelisted_addresses", { client_id: clientId });
    return this.makeResponse(200, "Wallet whitelist fetched successfully", whitelist);
  }
  async addUserWhitelist(data: any) {
    try {
      console.log(`addUserWhitelist`, data)
      const { clientId, address, token, chain, tag, userId } = data;
      const validateToken = await this.validateToken(userId, token)
      if (validateToken != true) {
        // return validateToken
      }
      const owner: any = await this.getCampanyOwner(clientId, userId)
      if (owner.length === 0) {
        // return this.makeResponse(403, "You are not authorized to perform this action, only the owner can add a wallet address");
      }

      const whitelist = await this.selectDataQuerySafe("whitelisted_addresses", { client_id: clientId, address });
      if (whitelist.length > 0) {
        return this.makeResponse(409, "Address already whitelisted");
      }
      await this.insertData("whitelisted_addresses", { client_id: clientId, address: address, chain: chain, tag: tag });
      return this.makeResponse(200, "Address added to whitelist successfully");
    } catch (error: any) {
      console.error("Error adding address to whitelist:", error);
      return this.makeResponse(500, "Error adding address to whitelist");
    }
  }

  async removeUserWhitelist(data: any) {
    const { clientId, address, token, userId } = data;
    const owner: any = await this.getCampanyOwner(clientId, userId)
    if (owner.length === 0) {
      // return this.makeResponse(403, "You are not authorized to perform this action, only the owner can add a wallet address");
    }

    const whitelist = await this.selectDataQuerySafe("whitelisted_addresses", { client_id: clientId, address });
    if (whitelist.length === 0) {
      return this.makeResponse(404, "Address not found in whitelist");
    }
    await this.deleteData("whitelisted_addresses", `client_id = '${clientId}' AND address = '${address}'`);
    return this.makeResponse(200, "Address removed from whitelist successfully");
  }

  async banks(currency: any) {
    if (currency == '') {
      return await this.callQuerySafe(`select * from banks where status=?`, ['ACTIVE'])
    }
    return await this.callQuerySafe(`select * from banks where currency=? and status=?`, [currency, 'ACTIVE'])
  }

  async getDashboardLogins(clientId: string) {
    try {
      const logins = await this.selectDataQuerySafe("client_logins", { client_id: clientId });
      return this.makeResponse(200, "Dashboard logins fetched successfully", logins);
    } catch (error: any) {
      console.error("Error fetching dashboard logins:", error);
      return this.makeResponse(500, "Error fetching dashboard logins");
    }
  }

  async resetDashboardLogin(data: any) {
    try {
      const { clientId, userId, email, newPassword } = data;

      // Check if the login exists
      const login = await this.selectDataQuerySafe("client_logins", { client_id: clientId, email });
      if (login.length === 0) {
        return this.makeResponse(404, "Invalid credentials");
      }

      // Encrypt the new password
      const encryptedPassword = CryptoJS.AES.encrypt(newPassword, SECRET_KEY).toString();
      this.saveOperationLog("RESET_PASSWORD", clientId, userId, "admin", "client_logins", userId, "", "")
      // Update the password
      await this.updateData("client_logins", `client_id = '${clientId}' AND email = '${email}'`, { password: encryptedPassword });

      this.sendEmail("PASSWORD_RESET", email);

      return this.makeResponse(200, "Dashboard login password reset successfully");
    } catch (error: any) {
      console.error("Error resetting dashboard login:", error);
      return this.makeResponse(500, "Error resetting dashboard login");
    }
  }

  async deleteDashboardLogin(data: any) {
    try {
      const { clientId, email } = data;

      // Check if the login exists
      const login = await this.selectDataQuerySafe("client_logins", { client_id: clientId, email });
      if (login.length === 0) {
        return this.makeResponse(404, "Invalid credentials");
      }

      // Delete the login
      await this.deleteData("client_logins", `client_id = '${clientId}' AND email = '${email}'`);

      return this.makeResponse(200, "Dashboard login deleted successfully");
    } catch (error: any) {
      console.error("Error deleting dashboard login:", error);
      return this.makeResponse(500, "Error deleting dashboard login");
    }
  }
  async createDashboardLogin(data: any) {
    try {
      const { email, first_name, last_name, client_id } = data;
      const role = data.role || "admin"; // Default to 'admin' if role is not provided
      // Generate a secure password that meets policy requirements
      const password = this.generateSecurePassword(12, true);

      const existingAdmin = await this.selectDataQuerySafe("clients", { client_id });
      if (existingAdmin.length == 0) {
        return this.makeResponse(409, "client doesn't exist");
      }


      // 🔐 Generate secure password hash using bcrypt
      const encryptedPassword = await this.generatePassword(password);

      // 🛠 Insert New Admin User
      const adminData = {
        client_id,
        email,
        password: encryptedPassword,
        role: role,
        first_name,
        last_name,
      };
      await this.insertData("client_logins", adminData);

      this.sendEmail("CLIENT_REGISTRATION", email, first_name, password);

      return this.makeResponse(200, "Client access created successfully");
    } catch (error: any) {
      console.error("Error creating admin user:", error);
      return this.makeResponse(500, "email already exists");
    }
  }

  async IpWhitelist(data: any) {
    const { clientId } = data;
    const existingIp = await this.selectDataQuerySafe("ip_whitelist", { client_id: clientId, status: 'active' });
    return this.makeResponse(200, "IP address already whitelisted", existingIp);
  }






  // async getStats(data: any) {
  //   try {

  //     const { clientId } = data;
  //     // const business: any=  await this.callQuery(`select * from client_logins where id = '${clientId}' `);
  //     // if(business.length === 0){
  //     //   return this.makeResponse(404, "Business not found");
  //     // }
  //     const transactions: any = await this.callQuery(`select * from transactions where client_id = '${data.client_id}' `);
  //     const revenue: any = await this.callQuery(`select sum(amount) as revenue from transactions where client_id = '${data.client_id}' AND trans_type='PUSH' `);
  //     const payouts: any = await this.callQuery(`select sum(amount) as payouts from transactions where client_id = '${data.client_id}' AND trans_type='PULL' `);
  //     const collections: any = await this.callQuery(`select sum(amount) as collections from transactions where client_id = '${data.client_id}' AND trans_type='PULL' `);

  //     const hardcodedStats = {
  //       collections: collections[0].collections,
  //       payouts: payouts[0].payouts,
  //       revenue: revenue[0].revenue,
  //       transactions: transactions.length,
  //     };
  //     return this.makeResponse(200, "Stats fetched successfully", hardcodedStats);
  //   } catch (error: any) {
  //     console.error("Error fetching stats:", error);
  //     return this.makeResponse(500, "Error fetching stats");
  //   }
  // }



  async addIpWhitelist(data: any) {
    try {
      console.log(`addIpWhitelist`, data)
      const { clientId, ip_address, api_key } = data;
      const validateToken = await this.validateToken(clientId, data.token)
      if (validateToken != true) {
        //  return validateToken
      }

      /*
      const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
      if (!ipRegex.test(ip_address.trim())) {
      //  return this.makeResponse(400, "Invalid IP address format");
      }
      */


      // Check if the client exists
      const client = await this.selectDataQuerySafe("clients", { client_id: clientId });
      if (client.length === 0) {
        return this.makeResponse(404, "Client not found");
      }

      // Check if the IP address is already whitelisted
      const existingIp: any = await this.selectDataQuerySafe("ip_whitelist", { client_id: clientId, ip_address });
      if (existingIp.length > 0) {
        console.log(`existingIp`, existingIp)
        return this.makeResponse(409, "IP address already whitelisted");
      }

      // Add the IP address to the whitelist
      await this.insertData("ip_whitelist", { client_id: clientId, ip_address: ip_address, api_key: api_key });

      return this.makeResponse(200, "IP address added to whitelist successfully");
    } catch (error: any) {
      console.error("Error adding IP to whitelist:", error);
      return this.makeResponse(500, "Error adding IP to whitelist");
    }
  }
  async validateToken(userId: string, token: string) {
    if (!token || token == '' || token == null) {
      return this.makeResponse(203, "2FA token is required");
    }
    if (!userId || userId == '' || userId == null) {
      return this.makeResponse(203, "2FA token is required, please login again");
    }
    if (token.length == 8) {
      const otpRs: any = await this.callQuerySafe("SELECT * FROM user_otp WHERE user_id = ? AND otp = ?", [userId, token]);
      if (otpRs.length == 0) {
        return this.makeResponse(203, "Invalid token");
      }
    } else if (token.length == 6) {
      const data: any = {};
      data.userId = userId;
      data.user_type = 'client';
      const client: any = await this.userFaAuthAccountStatus({ ...data, "user_type": 'client' })
      if (client.length === 0) {
        return this.makeResponse(404, "Unknown 2fa secret");
      }
      console.log(`client`, client)
      const responseData: any = TwoFactorAuthHelper.verifySecret(client[0].secret, token)
      if (!responseData?.status) {
        return this.makeResponse(203, "Invalid 2fa token");
      }
    } else {
      return this.makeResponse(203, "Invalid token");
    }
    return true
  }
  async validateUserToken(token: string, userId: string) {
    console.log(`userId`, userId)
    // console.log(`token`, token) // SECURITY: Removed token logging
    const validateToken = await this.validateToken(userId, token)
    if (validateToken != true) {
      return validateToken
    }
    return this.makeResponse(200, "Token validated successfully", { "status": true })
  }

  async sendUserToken(userId: string) {

    const data: any = {};
    data.clientId = userId;
    data.user_type = 'client';
    const client: any = await this.userFaAuthAccountStatus({ ...data, "user_type": 'client' })
    console.log(`client1`, client)
    if (client.length === 0) {
      const response = await this.sendUserAuthToken(userId)
      console.log(`client2`, response)

      if (response != false) {
        return response
      }

      if (client.length === 0) {
        return this.makeResponse(404, "Please setup 2FA to continue");
      }
    }
    return this.makeResponse(211, "user has 2FA enabled")



  }

  async removeIpWhitelist(data: any) {
    try {
      const { clientId, ip_address, token, userId } = data;
      const validateToken = await this.validateToken(userId, token)
      if (validateToken != true) {
        return validateToken
      }

      // Check if the client exists
      const client = await this.selectDataQuerySafe("clients", { client_id: clientId });
      if (client.length === 0) {
        return this.makeResponse(404, "Client not found");
      }

      // Check if the IP address is whitelisted
      const existingIp = await this.selectDataQuerySafe("ip_whitelist", { client_id: clientId, ip_address });
      if (existingIp.length === 0) {
        return this.makeResponse(404, "IP address not found in whitelist");
      }

      // Remove the IP address from the whitelist
      await this.deleteData("ip_whitelist", `client_id = '${clientId}' AND ip_address = '${ip_address}'`);

      this.sendEmail("IP_REMOVED", clientId, ip_address);
      return this.makeResponse(200, "IP address removed from whitelist successfully");
    } catch (error: any) {
      console.error("Error removing IP from whitelist:", error);
      return this.makeResponse(500, "Error removing IP from whitelist");
    }
  }

  async userConfirmLogin(email: string, password: string, verificationToken: string) {
    try {

      // 🔎 Check if Admin Exists
      const admin = await this.selectDataQuerySafe("client_logins", { email });
      if (admin.length === 0) {
        return this.makeResponse(404, "Invalid credentials");
      }

      // const encrypteNewdPassword = CryptoJS.AES.encrypt(password, SECRET_KEY).toString();
      // if (encrypteNewdPassword !== admin[0].password) {
      //   return this.makeResponse(400, "Invalid credentials");
      // }

      // Check if stored password exists and is valid
      if (!admin[0].password || typeof admin[0].password !== 'string') {
        return this.makeResponse(400, "Invalid credentials");
      }

      // Check if password is stored as bcrypt hash or old AES encryption
      let isPasswordValid = false;

      // First try bcrypt verification
      try {
        isPasswordValid = await this.verifyPassword(password, admin[0].password);
      } catch (error) {
        // If bcrypt fails, try old AES decryption
        try {
          const bytes = CryptoJS.AES.decrypt(admin[0].password, SECRET_KEY);
          const originalPassword = bytes.toString(CryptoJS.enc.Utf8);
          isPasswordValid = (originalPassword === password);
        } catch (decryptError) {
          console.error("Error decrypting password:", decryptError);
          isPasswordValid = false;
        }
      }

      if (!isPasswordValid) {
        return this.makeResponse(400, "Invalid credentials");
      }

      if (admin[0].default_password === "true") {
        const checkDefaultPasswordExpiry = await this.checkDefaultPasswordExpiry(admin[0].default_password_expiry, 4);
        if (!checkDefaultPasswordExpiry) {
          return this.makeResponse(400, "Your login token has expired. Please reset your password.");
        }
      }

      const profile = await this.getProfile(admin[0].client_id)
      const data: any = {};
      data.userId = admin[0].id;
      const userId = admin[0].id
      data.user_type = 'client';
      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'client' })
      if (client.length === 0) {
        return this.makeResponse(201, "Unknown 2fa secret");
      }

      const responseData: any = await TwoFactorAuthHelper.verifySecret(client[0].secret, verificationToken)
      if (!responseData?.status) {
        return this.makeResponse(400, "Invalid 2fa token");
      }

      // 🔑 Generate JWT Token
      const token = jwt.sign({ clientId: admin[0].client_id, userId: admin[0].id, email: admin[0].email, role: "client" }, process.env.JWT_SECRET!, {
        expiresIn: "10h",
      });
      const clientId = admin[0].client_id
      this.saveOperationLog("USER_LOGIN", clientId, userId, "admin", "client_logins", userId, "", "")


      return this.makeResponse(200, "client login successful", { ...profile[0], token });
    } catch (error: any) {
      console.error("Error in client login:", error);
      return this.makeResponse(500, "Error logging in");
    }
  }


  async userLogin(email: string, password: string) {
    try {

      // 🔎 Check if Admin Exists
      const admin: any = await this.selectDataQuerySafe("client_logins", { email });
      if (admin.length === 0) {
        return this.makeResponse(400, "Invalid credentials");
      }

      // Check if stored password exists and is valid
      if (!admin[0].password || typeof admin[0].password !== 'string') {
        return this.makeResponse(400, "Invalid credentials");
      }

      // Check if password is stored as bcrypt hash or old AES encryption
      let isPasswordValid = false;

      // First try bcrypt verification
      try {
        isPasswordValid = await this.verifyPassword(password, admin[0].password);
      } catch (error) {
        // If bcrypt fails, try old AES decryption
        try {
          const bytes: any = CryptoJS.AES.decrypt(admin[0].password, SECRET_KEY);
          const originalPassword: any = bytes.toString(CryptoJS.enc.Utf8);
          isPasswordValid = (originalPassword === password);
        } catch (decryptError) {
          console.error("Error decrypting password:", decryptError);
          isPasswordValid = false;
        }
      }

      if (!isPasswordValid) {
        return this.makeResponse(400, "Invalid credentials");
      }

      if (admin[0].default_password === "true") {

        const checkDefaultPasswordExpiry = await this.checkDefaultPasswordExpiry(admin[0].default_password_expiry, 4);
        if (!checkDefaultPasswordExpiry) {
          return this.makeResponse(400, "Your login token has expired. Please reset your password.");
        }
      }

      console.log(`adminInfo1`, admin)
      const profile = await this.getProfile(admin[0].client_id)
      const userId = admin[0].client_id

      const data: any = {};
      data.email = admin[0].email;
      data.userId = admin[0].id;
      data.user_type = 'client';

      const client: any = await this.userFaAuthAccountStatus(data)
      if (client[0]?.status === 'active') {
        return this.makeResponse(202, "Provide a 2fa token to login", { "2fa_status": client[0]?.status });
      }

      // 🔑 Generate JWT Token
      const token = jwt.sign({ clientId: admin[0].client_id, userId: admin[0].id, email: admin[0].email, role: "client" }, process.env.JWT_SECRET!, {
        expiresIn: "10h",
      });

      return this.makeResponse(200, "client login successful", { ...profile[0], token });
    } catch (error: any) {
      console.error("Error in admin login:", error);
      return this.makeResponse(500, "Error logging in");
    }
  }


  async getPendingApproval(data: any) {
    try {

      const admin: any = await this.selectDataQuerySafe("client_logins", { id: data?.userId });
      if (admin.length === 0) {
        return this.makeResponse(400, "Business account not found");
      }

      const client_id: any = admin[0]?.client_id;
      const transactions: any = await this.callQuerySafe(`select * from transactions where client_id = ? AND status = ? AND system_status != ? ORDER BY created_at DESC`, [client_id, 'PENDING_APPROVAL', 'PENDING_ADMIN_APPROVAL']);
      return this.makeResponse(200, "Pending approval fetched successfully", transactions);
    } catch (error: any) {

      console.error("Error fetching stats:", error);
      return this.makeResponse(500, "Error fetching stats");
    }
  }


  async approveTransactionPayment(data: any) {
    try {


      const { transaction_id } = data;
      const transaction: any = await this.selectDataQuerySafe("transactions", { trans_id: transaction_id });
      if (transaction.length === 0) {
        return this.makeResponse(400, "Transaction not found");
      }

      await this.updateData("transactions", `trans_id = '${transaction_id}'`, { status: "APPROVED" });
      const logsData = {
        client_id: transaction[0].client_id,
        user_id: data?.userId,
        status: "APPROVED",
        body: JSON.stringify({ transaction_id: transaction_id, approver_id: data?.userId, time: this.getMySQLDateTime_() }),
        created_on: this.getMySQLDateTime_()
      };
      await this.insertData("api_logs", logsData);
      return this.makeResponse(200, "Transaction approved successfully");
    } catch (error: any) {
      console.error("Error approving transaction:", error);
      return this.makeResponse(500, "Error approving transaction");
    }
  }


  async rejectTransactionPayment(data: any) {
    try {
      const { transaction_id } = data;
      const transaction: any = await this.selectDataQuerySafe("transactions", { trans_id: transaction_id });
      if (transaction.length === 0) {
        return this.makeResponse(400, "Transaction not found");
      }
      await this.updateData("transactions", `trans_id = '${transaction_id}'`, { status: "FAILED" });
      const logsData = {
        client_id: transaction[0].client_id,
        user_id: data?.userId,
        status: "REJECTED",
        body: JSON.stringify({ transaction_id: transaction_id, approver_id: data?.userId, time: this.getMySQLDateTime_() }),
        created_on: this.getMySQLDateTime_()
      };
      await this.insertData("api_logs", logsData);
      return this.makeResponse(200, "Transaction rejected successfully");
    } catch (error: any) {
      console.error("Error rejecting transaction:", error);
      return this.makeResponse(500, "Error rejecting transaction");
    }
  }


  async getStats(clientId: string, data: any) {
    try {
      const { start_date, end_date, currency } = data;

      let currencyFilter = "";
      if (currency) {
        currencyFilter = ` AND currency = '${currency}'`;
      }

      const transactions: any = await this.callQuerySafe(`select * from transactions where client_id = ? AND status='SUCCESS' ${currencyFilter} `, [clientId]);
      const transactions_pull: any = await this.callQuerySafe(`select SUM(amount) as amount from transactions where client_id = ? and trans_type='PULL' AND status='SUCCESS'${currencyFilter} `, [clientId]);
      const transactions_push: any = await this.callQuerySafe(`select SUM(amount) as amount from transactions where client_id = ? and trans_type='PUSH' AND status='SUCCESS'${currencyFilter} `, [clientId]);
      let transactions_pull_by_currency: any;
      let transactions_push_by_currency: any;
      let transactions_push_count: any = transactions.length;
      if (currency) {

        transactions_push_count = transactions.filter((transaction: any) => transaction.currency === currency).length;
        transactions_pull_by_currency = await this.callQuerySafe(`select SUM(amount) as amount from transactions where client_id = ? and trans_type='PULL' AND status='SUCCESS' AND currency = ? GROUP BY currency `, [clientId, currency]);
        transactions_push_by_currency = await this.callQuerySafe(`select SUM(amount) as amount from transactions where client_id = ? and trans_type='PUSH' AND status='SUCCESS' AND currency = ? GROUP BY currency`, [clientId, currency]);
      } else {

        transactions_pull_by_currency = await this.callQuerySafe(`select SUM(amount) as amount, currency from transactions where client_id = ? and trans_type='PULL' AND status='SUCCESS' GROUP BY currency `, [clientId]);
        transactions_push_by_currency = await this.callQuerySafe(`select SUM(amount) as amount, currency from transactions where client_id = ? and trans_type='PUSH' AND status='SUCCESS' GROUP BY currency`, [clientId]);
      }
      const accountBalances: any = await this.getAccountBalances(clientId)
      const hardcodedStats = {
        transactions_count: transactions_push_count,
        collections: transactions_pull[0].amount ?? 0,
        payouts: transactions_push[0].amount ?? 0,
        revenue: 0,
        transactions: transactions.length,
        collections_per_currency: transactions_pull_by_currency ?? [],
        payouts_per_currency: transactions_push_by_currency ?? [],
        account_balances: accountBalances ?? []
      };

      return this.makeResponse(200, "Stats fetched successfully", hardcodedStats);
    } catch (error: any) {
      console.error("Error fetching stats:", error);
      return this.makeResponse(500, "Error fetching stats");
    }
  }

  async sendWebhookUrl(data: any) {
    console.log(`data`, data)
    const { clientId, transId, event, webhookData } = data;
    const resp = await this.saveWebhook(clientId, transId, event, webhookData)
    return this.makeResponse(200, "Webhook sent successfully", resp);
  }

  async webhooks(clientId: string) {
    const client = await this.callRawQuery(`SELECT * FROM webhooks WHERE client_id = '${clientId}'`);
    return this.makeResponse(200, "success", client);
  }

  async addWebhook(data: any) {
    try {
      const { clientId, callback_url } = data

      // Validate URL format
      const urlPattern = /^https?:\/\/.+/;
      if (!urlPattern.test(callback_url)) {
        return this.makeResponse(400, "Invalid callback URL format. Must be a valid HTTPS URL");
      }

      const testWebhook = await this.testWebhook(callback_url)
      if (testWebhook != true) {
        return this.makeResponse(400, "Invalid callback URL, please make sure the callback url is valid and returns a 200 status code");
      }

      const hooks = await this.selectDataQuerySafe("webhooks", { client_id: clientId })
      if (hooks.length >= 3) {
        return this.makeResponse(403, "Maximum number of webhooks reached");

      }
      await this.insertData("webhooks", { client_id: clientId, callback_url });
      return this.makeResponse(200, "Webhook added successfully", data);
    } catch (error) {
      // Handle errors and return a proper response
      console.error("Error adding webhook:", error);
      return this.makeResponse(400, "Failed to add webhook, please make sure the callback url is valid and returns a 200 status code  ");
    }
  }
  async testWebhook(callback_url: any) {
    try {
      const response = await post(callback_url, {
        message: "Test webhook",
        status: 200
      })
      console.log(`response`, response)
      return true
    } catch (error: any) {
      console.error("Error testing webhook:", error);
      return error
    }
  }

  async deleteApiKey(data: any) {
    try {
      console.log(`DELETE`, data)

      const { clientId, id } = data
      await this.deleteData("api_keys", `id='${data.id}' and client_id='${clientId}'`);
      return this.makeResponse(200, "Webhook deleted", data);
    } catch (error) {
      // Handle errors and return a proper response
      console.error("Error adding webhook:", error);
      return this.makeResponse(500, "Failed to delete key");
    }
  }


  async deleteHook(data: any) {
    try {

      const { clientId, id } = data
      await this.deleteData("webhooks", `client_id='${clientId}' and id=${id}`);
      return this.makeResponse(200, "Webhook deleted", data);
    } catch (error) {
      // Handle errors and return a proper response
      console.error("Error adding webhook:", error);
      return this.makeResponse(500, "Failed to delete webhook");
    }
  }

  async getClientChains(clientId: string) {
    const client = await this.callRawQuery(`SELECT * FROM supported_chains`);
    return this.makeResponse(200, "Error fetching stats", client);
  }
  async getClientCurrencies(clientId: string) {
    // const client = await this.callQuery(`select * from client_currencies c inner join supported_currencies s  where s.asset_code = c.currency and c.client_id = '${clientId}'`);
    const client = await this.callRawQuery(`SELECT * FROM supported_currencies`);
    return this.makeResponse(200, "Error fetching stats", client);
  }

  async getclientBalance(clientId: any) {
    console.log(`getStatement`, clientId)

    const currencyInfo: any = await this.getClientCurrencies(clientId);
    const currencies = currencyInfo.data;

    // Get the balances from StellarService
    const balances: any = await new StellarService().getBalance(clientId) || [];

    // For each currency, find a matching balance; if not found, set it to "0"
    for (const currency of currencies) {
      const matchingBalance = balances.find((b: any) => currency.asset_code === b.code);
      currency.balance = matchingBalance ? matchingBalance.balance : "0";
      currency.oHoldBalance = await this.getOHoldBalance(clientId, currency.asset_code);
    }
    return this.makeResponse(200, "Client balances fetched successfully", currencies);
  }

  async getOHoldBalance(clientId: string, currency: string) {
    const oHoldBalance: any = await this.callRawQuery(`SELECT SUM(amount) AS amount FROM transactions WHERE client_id = '${clientId}' AND trans_type='PUSH' AND currency = '${currency}' AND status='ONHOLD'`);
    const balance = oHoldBalance.length > 0 ? oHoldBalance[0].amount : "0";
    if (balance == null) {
      return "0";
    }
    return balance;
  }


  // actual currency balances
  async getAccountBalances(clientId: string) {
    const currencyInfo: any = await this.getClientCurrencies(clientId);
    const currencies = currencyInfo.data;
    // Get the balances from StellarService
    const balances: any = await new StellarService().getBalance(clientId) || [];
    // For each currency, find a matching balance; if not found, set it to "0"
    for (const currency of currencies) {
      const matchingBalanceonHold = await this.getOHoldBalance(clientId, currency.asset_code);
      const matchingBalance = balances.find((b: any) => currency.asset_code === b.code);
      currency.balance = matchingBalance ? Number(matchingBalance.balance) - Number(matchingBalanceonHold) : "0";
    }
    return currencies;
  }

  async addClientCurrency(data: any) {
    try {
      const { clientId, currency } = data;
      // Check if the client exists
      const client = await this.selectDataQuerySafe("clients", { client_id: clientId });
      if (client.length === 0) {
        return this.makeResponse(404, "Client not found");
      }

      // Check if the currency is supported
      const supportedCurrency = await this.selectDataQuerySafe("supported_currencies", { currency });
      if (supportedCurrency.length === 0) {
        return this.makeResponse(400, "Currency not supported");
      }

      // Check if the client already has the currency
      const clientCurrency = await this.selectDataQuerySafe("client_currencies", { client_id: clientId, currency });
      if (clientCurrency.length > 0) {
        return this.makeResponse(409, "Client already has this currency");
      }

      // Add the currency to the client's account
      await this.insertData("client_currencies", { client_id: clientId, currency });

      return this.makeResponse(200, "Currency added to client successfully");
    } catch (error: any) {
      console.error("Error adding currency to client:", error);
      return this.makeResponse(500, "Error adding currency to client");
    }
  }

  async requestSwap(data: any) {
    try {
      const { clientId, from, to, currency, narration, pin, amount } = data;

      const fromAsset = "c" + currency;
      console.log(fromAsset)
      // Check if the client exists
      const client = await this.selectDataQuerySafe("clients", { client_id: clientId });
      if (client.length === 0) {
        return this.makeResponse(404, "Client not found");
      }

      // Check if the client has enough balance in fromCurrency
      const balances: any = await new StellarService().getBalance(clientId);
      console.log(balances)

      const fromCurrencyBalance = balances.find((balance: any) => balance.code === fromAsset);

      if (!fromCurrencyBalance || fromCurrencyBalance.balance < amount) {
        return this.makeResponse(400, "Insufficient balance");
      }

      if (parseFloat(amount) != parseFloat(amount)) {
        return this.makeResponse(400, "Amounts do not match");
      }

      const refId = this.getRandomString()
      const transactionData: any = {
        reference_id: refId,
        validation_id: refId,
        client_id: clientId,
        product_id: 1000,
        trans_id: this.getTransId(),
        amount: amount,
        trans_type: "SWAP",
        asset_code: fromAsset,
        sender_account: clientId,
        currency,
        receiver_account: clientId,
        memo: "SWAP",
        status: "pending",
      };
      await this.insertData("swap_requests", transactionData);

      const walletService = new WalletService();
      const swapResponse = await walletService.swapTokens(transactionData.trans_id)
      return swapResponse

    } catch (error: any) {
      console.error("Error requesting swap:", error);
      return this.makeResponse(500, "Error requesting swap");
    }
  }





  async addCurrencies() {
    try {
      const currencies = [
        { asset_code: "UGX", asset_issuer: "issuer_ugx", currency: "UGX" },
        { asset_code: "KES", asset_issuer: "issuer_kes", currency: "KES" },
        { asset_code: "TZS", asset_issuer: "issuer_tzs", currency: "TZS" }
      ];

      for (const currency of currencies) {
        await this.insertData("supported_currencies", currency);
      }

      return this.makeResponse(200, "Currencies added successfully");
    } catch (error: any) {
      console.error("Error adding currencies:", error);
      return this.makeResponse(500, "Error adding currencies");
    }
  }

  async getCurrencies() {
    try {
      const currencies = await this.selectDataQuerySafe("supported_currencies", {});
      return this.makeResponse(200, "Currencies fetched successfully", currencies);
    } catch (error: any) {
      console.error("Error fetching currencies:", error);
      return this.makeResponse(500, "Error fetching currencies");
    }
  }

  async updateAccountStatus(data: any) {
    try {
      const statusArray = ["live", "test"]
      const { new_status, clientId, userId } = data
      const profile = await this.getProfile(clientId)
      const kyc_status = profile[0].kyc_status


      if (new_status == "live" && kyc_status != "verified") {
        return this.makeResponse(500, "KYC not approved. please send KYC to switch to live");
      }


      if (!statusArray.includes(new_status)) {
        return this.makeResponse(500, "status is live or test");
      }

      await this.updateData("clients", `client_id = '${clientId}'`, { environment: new_status },);
      return await this.getClientById(clientId, userId);

    } catch (error: any) {
      console.error("Error resetting password:", error);
      return this.makeResponse(500, error.message || "Error resetting password");
    }
  }


  async changepassword(data: any) {
    try {

      const { userId, clientId, current_password, new_password, confirm_new_password } = data;
      if (!userId || !current_password || !new_password) {
        return this.makeResponse(400, "Account current and new passwords are required");
      }

      if (confirm_new_password != new_password) {
        return this.makeResponse(400, "Passwords don't match");
      }

      // Validate password policy
      const passwordValidation = this.validatePasswordPolicy(new_password);
      if (!passwordValidation.isValid) {
        return this.makeResponse(400, "Password does not meet security requirements", {
          errors: passwordValidation.errors,
          strength: passwordValidation.strength,
          score: passwordValidation.score
        });
      }

      // Verify current password
      const admin: any = await this.selectDataQuerySafe("client_logins", { id: userId, client_id: clientId });

      // Check stored password validity
      if (!admin[0]?.password || typeof admin[0].password !== 'string') {
        return this.makeResponse(400, "Current password is incorrect");
      }

      // Check if password is stored as bcrypt hash or old AES encryption
      let isCurrentPasswordValid = false;

      // First try bcrypt verification
      try {
        isCurrentPasswordValid = await this.verifyPassword(current_password, admin[0].password);
      } catch (error) {
        // If bcrypt fails, try old AES decryption
        try {
          const bytes = CryptoJS.AES.decrypt(admin[0].password, SECRET_KEY);
          const originalPassword = bytes.toString(CryptoJS.enc.Utf8);
          isCurrentPasswordValid = (originalPassword === current_password);
        } catch (decryptError) {
          console.error("Error decrypting password:", decryptError);
          isCurrentPasswordValid = false;
        }
      }

      if (!isCurrentPasswordValid) {
        return this.makeResponse(400, "Current password is incorrect");
      }

      // Generate secure password hash using bcrypt
      const hashedNewPassword = await this.generatePassword(new_password);
      await this.updateData("client_logins", `id = '${userId}' AND client_id = '${clientId}'`, { password: hashedNewPassword, default_password: "false", default_password_expiry: null });
      const email = admin[0].email
      this.sendEmail("PASSWORD_CHANGED", email);
      return this.makeResponse(200, "Password reset successfully");
    } catch (error: any) {
      console.error("Error resetting password:", error);
      return this.makeResponse(500, "Error resetting password");
    }
  }



  async getClientById(clientId: string, userId: string) {
    try {

      const defaultRole: any = await this.defaultRole();
      const defaultClientRole: any = await this.defaultClientRole();

      const userCount: any = await this.callQuerySafe(`select COUNT(*) as total_members from clients where client_id = '${clientId}'`);
      // const main_user: any = await this.callQuerySafe(`select c.* , l.id as user_id, l.last_name, l.first_name,l.email,l.role,l.default_password from clients c inner join client_logins l on c.client_id = l.client_id where c.client_id = '${clientId}' ORDER BY l.created_at DESC LIMIT 1`);
      const data: any = await this.callQuerySafe(`select c.* , l.id as user_id, l.last_name, l.first_name,l.email,l.role,l.default_password from clients c inner join client_logins l on c.client_id = l.client_id where c.client_id = '${clientId}' AND l.id='${userId}'`);
      // data[0].acc_verify - auto detmines the default account role and rights
      // include user roles and rights 
      if (data[0]?.role === defaultRole?.id || data[0]?.role === "admin") {

        data[0].role_details = defaultRole;
        data[0].access_rights = [];
        data[0].acc_verify = "admin";

      } else if (data[0]?.role === defaultClientRole?.id) {

        // to just view content on the dashboard and not edit      
        let rights: any = await this.callQuerySafe(`select a.*, a.name as access_right_name from access_rights a`);
        if (rights.length > 0) {

          // restricted viewing of content only
          const allowedRightsIds: any = ["8b53f1d2-608d-11f0-a6f1-00155dfa6a43", "8b53f0c8-608d-11f0-a6f1-00155dfa6a43", "8b53f266-608d-11f0-a6f1-00155dfa6a43"]
          rights = rights.filter((role: any) => allowedRightsIds.includes(role.id));
        }
        data[0].role_details = defaultClientRole;
        data[0].access_rights = rights;
        data[0].acc_verify = "client";

      } else {


        const role: any = await this.callQuerySafe(`SELECT * FROM roles WHERE id = ?`, [data[0].role]);
        const rights: any = await this.callQuerySafe(`SELECT ar.*, a.name as access_right_name from role_access_rights ar LEFT JOIN access_rights a on ar.access_right_id = a.id WHERE ar.role_id = ? AND ar.deleted_at IS NULL`, [data[0].role]);
        data[0].role_details = role[0] ?? defaultRole;
        data[0].access_rights = rights;
        data[0].acc_verify = "";
      }

      // check if the account is verified
      data[0].team_count = userCount[0]?.total_members ?? 1;
      if (userCount[0]?.total_members === 2 && data[0].role_details?.id === defaultRole?.id) {
        data[0].acc_verify = 'admin';
      }
      return this.makeResponse(200, "SUCCESS", data[0]);

    } catch (error: any) {

      console.error("rror getting profile details:", error);
      return this.makeResponse(500, "Error getting profile details");
    }
  }


  // OAauthLogin is used to login to the dashboard using the api key
  async OAauthLogin(req: any) {
    try {
      const { api_key, public_key, secret_key } = req;
      const hashedSecretKey = CryptoJS.SHA256(secret_key + SECRET_KEY).toString();
      // console.log(hashedSecretKey) // SECURITY: Removed secret key logging

      const admin: any = await this.callQuerySafe(
        "SELECT * FROM api_keys WHERE api_key = ? AND secret_key = ?",
        [api_key, hashedSecretKey]
      );
      if (admin.length === 0) {
        return this.makeResponse(404, "Invalid credentials", req);
      }

      const ip_addresses = admin[0].ip_addresses
      const client_id = admin[0].client_id
      let message = "success"
      if (!ip_addresses || ip_addresses.length == 0) {
        message = "Security alert: These credentials have not whitelist and will be disabled soon"
      }

      const token = jwt.sign({ clientId: admin[0].client_id, userId: admin[0].api_key, apiKey: admin[0].api_key, user: "API", role: "API" }, process.env.JWT_SECRET!, {
        expiresIn: "6h",
      });

      //  setItem(`${client_id}_jwt_token`, token)
      const data = {
        access_token: token,
        token_type: 'Bearer',
        expires_in: 21600,
        message: message
      }

      return this.makeResponse(200, "success", data);
    } catch (error: any) {
      console.error("Error in admin login:", error);
      return this.makeResponse(500, "Error logging in");
    }
  }


  async getProfile(client_id: string) {
    return this.selectDataQuerySafe("clients", { client_id });
  }

  async adminLogin(email: string, password: string) {
    try {

      // 🔎 Check if Admin Exists
      const admin = await this.selectDataQuerySafe("client_logins", { email, role: 'admin' });
      if (admin.length === 0) {
        return this.makeResponse(400, "Invalid credentials");
      }
      console.log(`adminInfo1`, admin)
      const profile = await this.getProfile(admin[0].client_id)
      const userId = admin[0].client_id

      const adminClinet = await this.selectDataQuerySafe("clients", { client_id: admin[0].client_id });
      if (adminClinet.length === 0) {
        return this.makeResponse(400, "Failed to login! Contact support team for help");
      }

      // Check if stored password exists and is valid
      if (!admin[0].password || typeof admin[0].password !== 'string') {
        return this.makeResponse(400, "Invalid credentials");
      }

      // Check if password is stored as bcrypt hash or old AES encryption
      let isPasswordValid = false;

      // First try bcrypt verification
      try {
        isPasswordValid = await this.verifyPassword(password, admin[0].password);
      } catch (error) {
        // If bcrypt fails, try old AES decryption
        try {
          const decryptedPassword = CryptoJS.AES.decrypt(admin[0].password, SECRET_KEY).toString(CryptoJS.enc.Utf8);
          isPasswordValid = (decryptedPassword === password);
        } catch (decryptError) {
          console.error("Error decrypting password:", decryptError);
          isPasswordValid = false;
        }
      }

      if (!isPasswordValid) {
        return this.makeResponse(400, "Invalid credentials");
      }

      // 🔑 Generate JWT Token
      const token = jwt.sign({ clientId: admin[0].client_id, userId: admin[0].id, email: admin[0].email, role: "client" }, process.env.JWT_SECRET!, {
        expiresIn: "10h",
      });
      console.log(`adminInfo2`, { clientId: admin[0].client_id, userId: admin[0].id, email: admin[0].email, role: "client" })

      return this.makeResponse(200, "client login successful", { ...profile[0], token });
    } catch (error: any) {
      console.error("Error in admin login:", error);
      return this.makeResponse(500, error.message || "Error logging in");
    }
  }

  async editApiKeys(data: any) {
    try {
      const { key_name, id, clientId, userId, token, permissions, ip_addresses, role } = data;
      this.saveOperationLog("UPDATE_API_KEY", clientId, userId, "client", "api_keys", id, data, data.ip || "")

      this.checkAllowedAccess(role, "client")
      const validateToken = await this.validateToken(userId, token)
      if (validateToken != true) {
        return validateToken
      }

      const getKey = await this.selectDataQuerySafe("api_keys", { id: id, client_id: clientId })
      if (getKey.length == 0) {
        return this.makeResponse(400, "API key to update not found")
      }
      const api_key = getKey[0].api_key

      const clientInfo = await this.selectDataQuerySafe("clients", { client_id: clientId })
      if (clientInfo.length == 0) {
        return this.makeResponse(400, "Client not found")
      }
      if (!ip_addresses || ip_addresses.length === 0) {
        return this.makeResponse(400, "At least one IP address must be whitelisted to enable withdraw permission.");
      }

      let permissions_string = "READ";
      if (permissions && permissions.length > 0) {
        permissions_string = permissions.join(",");
      }

      this.updateData("api_keys", `api_key = '${api_key}' AND client_id = '${clientId}'`, { key_name: key_name, permissions: permissions_string, ip_addresses: ip_addresses })
      return this.makeResponse(200, "API key updated successfully")

    } catch (error: any) {
      console.error("Error in edit API keys:", error);
      return this.makeResponse(500, error.message || "Error editing API keys");
    }
  }

  async generateApiKeys(data: any) {
    try {
      const { key_name, clientId, userId, token, permissions, ip_addresses, role } = data;
      this.saveOperationLog("GENERATE_API_KEY", clientId, userId, "client", "api_keys", clientId, data, data.ip || "")

      this.checkAllowedAccess(role, "client") // must be called inside a try catch block
      const validateToken = await this.validateToken(userId, token)
      if (validateToken != true) {
        return validateToken
      }

      const clientInfo = await this.selectDataQuerySafe("clients", { client_id: clientId })
      if (clientInfo.length == 0) {
        return this.makeResponse(400, "Client not found")
      }

      // Validate permissions
      if (!data.permissions || !Array.isArray(data.permissions) || data.permissions.length === 0) {
        return this.makeResponse(400, "At least one permission must be selected");
      }

      // Check IP requirement
      const restrictedPerms = ["COLLECTIONS", "TRADING", "WITHDRAW"];
      const requiresIP = data.permissions.some((access: any) => restrictedPerms.includes(access));


      let permissionsCollection: any = await this.parseContentData(data.permissions);
      permissionsCollection = JSON.stringify(permissionsCollection);

      // Check existing API keys count for this client
      const existingKeys = await this.selectDataQuerySafe("api_keys", { client_id: clientId });
      if (existingKeys.length >= 2) {
        return this.makeResponse(400, "Maximum limit of 2 API keys reached. Please revoke existing keys to generate new ones.");
      }
      const savedEnvironment = clientInfo[0].environment

      const secretKey = savedEnvironment + "_sec-" + uuidv4() + uuidv4();

      const hashedSecretKey = CryptoJS.SHA256(secretKey + SECRET_KEY).toString();

      // 🔑 Generate API Key

      let ip_addresses_string = ip_addresses
      ip_addresses_string = ip_addresses_string.replace(/\s+/g, "");
      let permissions_string = "read";
      if (permissions && permissions.length > 0) {
        permissions_string = permissions.join(",");
      }
      // Check if user has 'withdraw' permission, and if so, ensure at least one IP is whitelisted
      if (permissions && permissions.includes('withdraw')) {
        // Check if there is at least one IP address provided
        if (!ip_addresses || ip_addresses.length === 0) {
          return this.makeResponse(400, "At least one IP address must be whitelisted to enable withdraw permission.");
        }
      }


      const apiKey = savedEnvironment + "_key-" + uuidv4();


      const apiKeyData = {
        key_name: key_name,
        client_id: clientId,
        secret_key: hashedSecretKey,
        api_key: apiKey,
        permissions: permissions_string,
        ip_addresses: ip_addresses_string

      };

      // console.log(`generatedkeys`, apiKeyData) // SECURITY: Removed API key logging

      await this.insertData("api_keys", apiKeyData);
      return this.makeResponse(200, "API keys generated successfully", {
        client_id: clientId,
        secret_key: secretKey,
        api_key: apiKey
      });

    } catch (error: any) {
      console.error("Error generating API keys:", error);
      return this.makeResponse(500, error.message || "Server error");
    }
  }

  async getWallet(client_id: any) {
    const wallet = await this.createWallet(client_id)
    return this.makeResponse(200, "success", wallet);
  }

  async getRailsTransactions(client_id: any) {
    const wallet: any = await this.createWallet(client_id)
    const walletAddress = wallet?.data ?? ""

    if (walletAddress == "") {
      return this.makeResponse(400, "wallet not known", wallet);
    }
    return this.makeResponse(200, "success", { wallet: wallet });

  }

  async paymentMethods() {
    const cryptoAddresses = await this.callRawQuery(`SELECT * FROM dp_crypto_deposits`);
    const fiatAddresses = await this.callRawQuery(`SELECT * FROM dp_bank_transfers`);
    const rsp = {
      bank: fiatAddresses,
      stable: cryptoAddresses
    }
    return this.makeResponse(200, "success", rsp);
  }


  async reset2FaRequest(data: any) {
    try {
      const { email } = data;
      const existingUser: any = await this.getUserClientLogin(email);
      if (existingUser.length === 0) {
        return this.makeResponse(404, "Invalid credentials");
      }

      const requestChecker: any = await this.callQuerySafe("SELECT * FROM reset_credentials_requests WHERE user_id = ? AND used = ? AND reset_type = ?", [existingUser[0].id, 0, '2FA']);
      if (requestChecker.length > 0) {
        return this.makeResponse(203, "The 2Fa reset request has already been sent to the admin. Please wait for their approval.");
      }

      const user_id = existingUser[0].client_id
      const otp = await this.getOtp(email, user_id);
      this.sendEmail("RESET_2FA_REQUEST", email, "", otp);
      await this.saveOperationLog("INSERT", existingUser[0].client_id, existingUser[0].id, "client", "user_otp", "", { email: email, otp: otp }, "1")
      return this.makeResponse(200, "2Fa reset OTP was sent to your email");

    } catch (err: any) {
      console.log("reset 2Fa error ---- ", err.message, err)
      return this.makeResponse(203, "Error processing request");
    }
  }


  async reset2FaConfirm(data: any) {
    try {
      const { otp, email } = data;


      const otpRs: any = await this.callQuerySafe("SELECT * FROM user_otp WHERE email = ? AND otp = ?", [email, otp]);
      if (otpRs.length == 0) {
        return this.makeResponse(203, "OTP not found");
      }
      const user_id = otpRs[0]['user_id']

      const existingUser = await this.getUserClientLogin(email);
      if (existingUser.length == 0) {
        return this.makeResponse(404, "Invalid credentials");
      }

      const client_: any = await this.callQuerySafe("SELECT * FROM  user_2fa WHERE user_id = ? AND user_type = ? ", [existingUser[0].id, 'client']);
      if (client_.length == 0) {
        return this.makeResponse(203, "Account 2FA is not setup. Contact support for help");
      }

      const client: any = await this.callQuerySafe("SELECT * FROM clients WHERE client_id = ? ", [existingUser[0].client_id]);
      if (client.length == 0) {
        return this.makeResponse(203, "client not found");
      }

      const request_initiator = (client[0].contact_email === email) ? 'CLIENT_ADMIN' : 'TEAM'
      const request_approver = (client[0].contact_email === email) ? 'SYSTEM_ADMIN' : 'CLIENT_ADMIN'
      const adminResetData = {
        id: uuidv4(),
        user_id: existingUser[0].id,
        client_id: existingUser[0].client_id,
        reset_type: "2FA",
        request_initiator: request_initiator,
        request_approver: request_approver,
        expires_at: this.getMySQLDateTime_future(6, 'hours'),
        used: false,
        created_at: this.getMySQLDateTime_(),
        updated_at: this.getMySQLDateTime_()
      };
      await this.insertData("reset_credentials_requests", adminResetData);
      await this.saveOperationLog("INSERT", existingUser[0].client_id, existingUser[0].id, "client", "reset_credentials_requests", "", adminResetData, "1")
      return this.makeResponse(200, "2Fa resetrequest was sent to admin");

    } catch (err) {
      console.log(err)
      return this.makeResponse(203, "Error processing request");

    }
  }



  async resetPasswordRequest(data: any) {
    try {
      const { email } = data;
      const existingUser: any = await this.getUserClientLogin(email);
      if (existingUser.length === 0) {
        return this.makeResponse(404, "Invalid credentials");
      }

      const requestChecker: any = await this.callQuerySafe("SELECT * FROM reset_credentials_requests WHERE user_id = ? AND used = ? AND reset_type = ?", [existingUser[0].id, 0, 'PASSWORD']);
      if (requestChecker.length > 0) {
        return this.makeResponse(203, "The password reset request has already been sent to the admin. Please wait for their approval.");
      }

      const user_id = existingUser[0].client_id
      const otp = await this.getOtp(email, user_id);
      this.sendEmail("RESET_PASSWORD_REQUEST", email, "", otp);
      await this.saveOperationLog("INSERT", existingUser[0].client_id, existingUser[0].id, "client", "user_otp", "", { email: email, otp: otp }, "1")
      return this.makeResponse(200, "Password reset OTP was successfully sent to your email");

    } catch (err: any) {
      console.log("reset password error ---- ", err.message, err)
      return this.makeResponse(203, "Error processing request");
    }
  }

  async resetPassword(data: any) {
    try {
      const { otp, email, newPassword } = data;

      // Validate password policy
      const passwordValidation = this.validatePasswordPolicy(newPassword);
      if (!passwordValidation.isValid) {
        return this.makeResponse(400, "Password does not meet security requirements", {
          errors: passwordValidation.errors,
          strength: passwordValidation.strength,
          score: passwordValidation.score
        });
      }

      const otpRs: any = await this.callQuerySafe("SELECT * FROM user_otp WHERE email = ? AND otp = ?", [email, otp]);
      if (otpRs.length == 0) {
        return this.makeResponse(203, "OTP not found");
      }
      const user_id = otpRs[0]['user_id']

      const existingUser = await this.getUserClientLogin(email);
      if (existingUser.length == 0) {
        return this.makeResponse(404, "Invalid credentials");
      }
      const first_name = existingUser[0].name

      // Generate secure password hash using bcrypt
      const hashedNewPassword = await this.generatePassword(newPassword);

      const updates: any = {
        password: hashedNewPassword
      };
      await this.updateData('client_logins', `client_id = '${user_id}'`, updates);

      this.sendEmail("RESET_PASSWORD_COMPLETE", email, first_name);
      return this.makeResponse(200, "Password was successfully updated");

    } catch (err) {
      console.log(err)
      return this.makeResponse(203, "Error processing request");

    }
  }

  async resetPasswordConfirm(data: any) {
    try {
      const { otp, email } = data;


      const otpRs: any = await this.callQuerySafe("SELECT * FROM user_otp WHERE email = ? AND otp = ?", [email, otp]);
      if (otpRs.length == 0) {
        return this.makeResponse(203, "OTP not found");
      }
      const user_id = otpRs[0]['user_id']

      const existingUser = await this.getUserClientLogin(email);
      if (existingUser.length == 0) {
        return this.makeResponse(404, "Invalid credentials");
      }

      const client: any = await this.callQuerySafe("SELECT * FROM clients WHERE client_id = ? ", [existingUser[0].client_id]);
      if (client.length == 0) {
        return this.makeResponse(203, "client not found");
      }
      const request_initiator = (client[0].contact_email === email) ? 'CLIENT_ADMIN' : 'TEAM'
      const request_approver = (client[0].contact_email === email) ? 'SYSTEM_ADMIN' : 'CLIENT_ADMIN'

      const adminResetData = {
        id: uuidv4(),
        user_id: existingUser[0].id,
        client_id: existingUser[0].client_id,
        reset_type: "PASSWORD",
        request_initiator: request_initiator,
        request_approver: request_approver,
        expires_at: this.getMySQLDateTime_future(6, 'hours'),
        used: false,
        created_at: this.getMySQLDateTime_(),
        updated_at: this.getMySQLDateTime_()
      };
      await this.insertData("reset_credentials_requests", adminResetData);
      await this.saveOperationLog("INSERT", existingUser[0].client_id, existingUser[0].id, "client", "reset_credentials_requests", "", adminResetData, "1")
      return this.makeResponse(200, "Password reset request was successfully sent to admin");

    } catch (err) {
      console.log(err)
      return this.makeResponse(203, "Error processing request");

    }
  }







  async resetTeamMemberPasswordRequest(data: any) {
    try {
      const { otp, email, newPassword } = data;

      // Validate password policy
      const passwordValidation = this.validatePasswordPolicy(newPassword);
      if (!passwordValidation.isValid) {
        return this.makeResponse(400, "Password does not meet security requirements", {
          errors: passwordValidation.errors,
          strength: passwordValidation.strength,
          score: passwordValidation.score
        });
      }

      const otpRs: any = await this.callQuerySafe("SELECT * FROM user_otp WHERE email = ? AND otp = ?", [email, otp]);
      if (otpRs.length == 0) {
        return this.makeResponse(203, "OTP not found");
      }
      const user_id = otpRs[0]['user_id']

      const existingUser = await this.getUserClientLogin(email);
      if (existingUser.length == 0) {
        return this.makeResponse(404, "Invalid credentials");
      }
      const first_name = existingUser[0].name

      // Generate secure password hash using bcrypt
      const hashedNewPassword = await this.generatePassword(newPassword);

      const updates: any = {
        password: hashedNewPassword
      };
      await this.updateData('client_logins', `client_id = '${user_id}'`, updates);

      this.sendEmail("RESET_PASSWORD_COMPLETE", email, first_name);
      return this.makeResponse(200, "Password was successfully updated");

    } catch (err) {
      console.log(err)
      return this.makeResponse(203, "Error processing request");

    }
  }


  async getcredentialsReset(data: any) {
    try {

      const { clientId } = data;
      // Pagination defaults
      const page = data?.page && Number(data.page) > 0 ? Number(data.page) : 1;
      const limit = data?.size && Number(data.size) > 0 ? Number(data.size) : 8;
      const offset = (page - 1) * limit;

      // Build where clause
      let whereClause = `WHERE rcr.request_approver = 'CLIENT_ADMIN' AND rcr.used != 1 AND rcr.client_id = '${clientId}'`;

      // if  data?.reset_type is not null then add it to the where clause
      if (data?.reset_type && data?.reset_type !== null) {
        whereClause += ` AND rcr.reset_type = '${data?.reset_type}'`;
      }

      // Add search filter if provided
      if (data?.search && typeof data.search === "string" && data.search.trim() !== "") {
        const searchStr = data.search.trim().replace(/'/g, "''");
        whereClause += ` AND (cl.first_name  LIKE '%${searchStr}%' OR cl.last_name  LIKE '%${searchStr}%'  LIKE '%${searchStr}%' OR rcr.user._id LIKE '%${searchStr}%' OR rcr.client_id LIKE '%${searchStr}%' OR rcr.status LIKE '%${searchStr}%')`;
      }

      // Get total count for pagination
      const totalItemsResult: any = await this.callQuerySafe(`
        SELECT COUNT(*) AS total FROM reset_credentials_requests rcr 
        LEFT JOIN client_logins cl ON rcr.user_id = cl.id 
        LEFT JOIN clients c ON rcr.client_id = c.client_id 
        ${whereClause}
      `);

      const totalItems = totalItemsResult[0]?.total || 0;
      const totalPages = Math.ceil(totalItems / limit);


      // Fetch paginated reset password requests
      const passwordResetRequests: any = await this.callQuerySafe(`
        SELECT 
          rcr.*,
          cl.id as login_id,
          cl.first_name,
          cl.last_name,
          cl.email as login_email,
          c.business_name
        FROM reset_credentials_requests rcr
        LEFT JOIN client_logins cl ON rcr.user_id = cl.id
        LEFT JOIN clients c ON rcr.client_id = c.client_id
        ${whereClause}
        ORDER BY rcr.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `);

      // Calculate pagination properties
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      const paginationInfo = {
        items: passwordResetRequests,
        pagination: {
          current_page: page,
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
          hasNextPage,
          hasPrevPage,
          next_page: hasNextPage ? page + 1 : null,
          previous_page: hasPrevPage ? page - 1 : null
        }
      };

      return this.makeResponse(200, "Password reset requests fetched successfully", paginationInfo);
    } catch (err: any) {

      console.log("Error getting credentials reset request:", err);
      return this.makeResponse(500, "Error getting credentials reset request");
    }
  }




  async twoFaAuthAccountStatus(data: any) {
    try {

      if (data?.userId === undefined) {

        return this.makeResponse(409, "Unknown user account");
      }

      let dataStatus: any = { status: '' }
      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'client' })
      if (client[0]?.status) {

        dataStatus = { status: client[0].status }
      }
      return this.makeResponse(200, "user 2fa status successfully", dataStatus);

    } catch (error: any) {

      console.error("Error creating system user:", error);
      return this.makeResponse(500, "Error creating system user");
    }
  }

  async twoFaAuthAccountCode(data: any): Promise<any> {
    try {
      if (!data?.userId) return this.makeResponse(400, "Unknown user account");

      const client = await this.userFaAuthAccountStatus({ ...data, user_type: 'client' });
      if (client.length > 0) {
        if (client[0]?.secret) {
          return this.makeResponse(200, "User 2fa code created successfully", { code: client[0].secret, url: client[0].qr_code });
        }
      }


      const admin: any = await this.selectDataQuerySafe("client_logins", { id: data?.userId });
      data.user_type = 'client';
      data.clientEmail = admin[0].email;
      const authCode = await TwoFactorAuthHelper.generateSecret(data);
      const userData = {
        id: uuidv4(),
        user_id: data.userId,
        secret: authCode.secret,
        user_type: 'client',
        qr_code: authCode.qrCode,
        code: authCode?.data?.data?.hex,
        status: 'pending'
      };


      await this.insertData("user_2fa", userData);
      return this.makeResponse(200, "User 2fa setup successfully", { code: authCode?.data?.data?.base32, url: authCode.qrCode });
    } catch (error: any) {
      console.error("Error generating user 2fa code:", error);
      return this.makeResponse(500, "Error generating user 2fa code");
    }
  }


  async twoFaAuthVerify(id: string, data: any) {
    try {

      if (data?.userId === undefined) {
        return this.makeResponse(400, "Unknown user account");
      }

      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'client' })
      if (client.length === 0) {

        data = { status: client[0].secret }
        return this.makeResponse(400, "Unknown 2fa secret");
      }

      const responseData: any = await TwoFactorAuthHelper.verifySecret(client[0].secret, data?.token)
      if (responseData?.status) {

        const userData = {
          status: 'active'
        };
        await this.updateData("user_2fa", `user_id = '${data?.userId}'`, userData);

        return this.makeResponse(200, "User user 2fa saved successfully", responseData);
      }

      return this.makeResponse(200, "User user 2fa failed", responseData);
    } catch (error: any) {

      console.error("Error creating system user:", error);
      return this.makeResponse(500, "Error creating system user");
    }
  }

  async twoFaAuthUpdate(id: string, data: any) {
    try {

      if (data?.userId === undefined) {

        return this.makeResponse(400, "Unknown user account");
      }

      if (!['active', 'inactive'].includes(data.status)) {
        return this.makeResponse(400, `Status must be either 'active' or 'inactive'`);
      }

      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'client' })
      if (client.lenght === 0) {

        data = { status: client[0].secret }
        return this.makeResponse(400, "Unknown 2fa secret");
      }
      const theData = {
        status: data?.status
      }
      this.updateData("user_2fa", `user_id = '${data?.userId}' AND deleted_at IS NULL`, theData)
      if (data?.status === 'active') {

        return this.makeResponse(200, "User 2fa  has been activated successfully");
      } else {
        return this.makeResponse(200, "User 2fa has been deactivated successfully");
      }
    } catch (error: any) {

      console.error("Error creating system user:", error);
      return this.makeResponse(500, "Error creating system user");
    }
  }

  async getBaseCurrencyRate(base_currency: string) {
    try {
      const prices: any = (await this.callRawQuery(`
      SELECT id, enabled, hasCrypto, referencePrice, base_currency, 
             quote_currency, pair, markup, markdown, created_at, updated_at 
      FROM exchange_rates 
      WHERE enabled = 1 AND base_currency = '${base_currency}'
    `));
      const pricesData = await Promise.all(
        prices.map(async (currencyPair: any) => {
          let fBestExchangeRate;
          let rateDetails: any = {};

          rateDetails = await ratesService.getRates({ quote: currencyPair.base_currency, base: currencyPair?.quote_currency });
          fBestExchangeRate = (rateDetails?.status) ? 1 / Number(rateDetails?.price) : "";

          return {
            ...currencyPair,
            current_exchange_rate: fBestExchangeRate
          };
        })
      );
      return this.makeResponse(200, "Exchange pair prices fetched successfully", pricesData);
    } catch (error: any) {
      console.error("Error fetching app pair price:", error);
      return this.makeResponse(500, "Error fetching app pair prices");
    }
  }


  async getPairsExchangeAmount(data: any) {
    try {
      const prices: any = (await this.callRawQuery(`
      SELECT id, enabled, hasCrypto, referencePrice, base_currency,  quote_currency, pair, markup, markdown, created_at, updated_at 
      FROM exchange_rates WHERE enabled = 1 AND base_currency = '${data.base_currency}' AND quote_currency = '${data.quote_currency}'
    `));

      if (prices.length === 0) {
        return this.makeResponse(404, "Exchange pair not found");
      }


      if (prices[0].updated_at) {
        const updatedAt = new Date(prices[0].updated_at);
        const now = new Date();
        const diffInMinutes = (now.getTime() - updatedAt.getTime()) / (1000 * 60);

        if (diffInMinutes < 1) {
          const cachedRate = prices[0].referencePrice || "";
          const fBestExchangeRate = cachedRate ? Number(cachedRate) : "";
          const fBestExchangeRateAmount = fBestExchangeRate ? (fBestExchangeRate * data.amount) : "";

          const result = {
            rate: fBestExchangeRate,
            exchangedAmount: fBestExchangeRateAmount,
            source: "cached"
          };
          return this.makeResponse(200, "Exchange pair amount calculated successfully (cached)", result);
        }
      }

      const rateDetails: any = await ratesService.getRates({ quote: data.quote_currency, base: data?.base_currency });
      console.log("rateDetails", rateDetails);
      const fBestExchangeRate: any = (rateDetails?.status) ? Number(rateDetails?.price) : "";
      const fBestExchangeRateAmount: any = (fBestExchangeRate * data.amount);

      const result = {
        rate: fBestExchangeRate,
        exchangedAmount: fBestExchangeRateAmount
      };

      return this.makeResponse(200, "Exchange pair amount calculated successfully", result);
    } catch (error: any) {
      console.error("Error fetching app pair price:", error);
      return this.makeResponse(500, "Error fetching app pair prices");
    }
  }

  async getRolesAccessRights() {
    try {
      const savedType: any = await this.callRawQuery(`
        SELECT r.*
        FROM access_rights r WHERE r.type = 'client' AND r.deleted_at IS NULL
        ORDER BY r.created_at DESC
      `);
      return this.makeResponse(200, "Role access rights fetched successfully", savedType);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching role access rights");
    }
  }

  async getRoles(data: any) {
    try {
      const admin: any = await this.selectDataQuerySafe("client_logins", { id: data?.userId });
      if (admin.length === 0) {
        return this.makeResponse(400, "Business account not found");
      }
      const client_id: any = admin[0]?.client_id;
      const savedType: any = await this.callRawQuery(`
        SELECT * FROM roles WHERE deleted_at IS NULL AND client_id = '${client_id}' ORDER BY created_at DESC
      `);

      // add default role to the list
      const defaultRole: any = await this.defaultRole();
      savedType.push(defaultRole);

      if (savedType.length > 0) {
        for (const role of savedType) {
          const accessRights = await this.callRawQuery(`
            SELECT rar.access_right_id as role_access_rights_id, 
                   rar.role_id as role_id, 
                   ar.name as name, 
                   ar.status as access_rights_status
            FROM access_rights ar
            INNER JOIN role_access_rights rar ON ar.id = rar.access_right_id
            WHERE rar.role_id = '${role.id}'
            AND rar.status = 'active'  
            AND ar.deleted_at IS NULL 
            AND rar.deleted_at IS NULL
          `);
          role.access_rights = accessRights;
        }
      }


      return this.makeResponse(200, "Role access rights fetched successfully", savedType);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching role access rights");
    }
  }

  async getRole(id: string, data: any) {
    try {
      const admin: any = await this.selectDataQuerySafe("client_logins", { id: data?.userId });
      if (admin.length === 0) {
        return this.makeResponse(400, "Business account not found");
      }
      const client_id: any = admin[0]?.client_id;
      const savedType: any = await this.callRawQuery(`
        SELECT * FROM roles WHERE deleted_at IS NULL AND client_id = '${client_id}' AND id = '${id}' ORDER BY created_at DESC
      `);


      if (savedType.length > 0) {
        for (const role of savedType) {
          const accessRights = await this.callRawQuery(`
             SELECT rar.access_right_id as role_access_rights_id, 
                   rar.role_id as role_id, 
                   ar.name as name, 
                   ar.status as access_rights_status
            FROM access_rights ar
            INNER JOIN role_access_rights rar ON ar.id = rar.access_right_id
            WHERE rar.role_id = '${savedType[0].id}'
            AND rar.status = 'active'  
            AND ar.deleted_at IS NULL 
            AND rar.deleted_at IS NULL
          `);
          role.access_rights = accessRights;
        }
      }
      return this.makeResponse(200, "Role access rights fetched successfully", savedType[0]);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching role access rights");
    }
  }

  async addRole(data: any) {
    try {

      console.log("data", data)
      const admin: any = await this.selectDataQuerySafe("client_logins", { id: data?.userId });

      console.log("admin", admin)
      if (admin.length === 0) {
        return this.makeResponse(400, "Business account not found");
      }
      const client_id: any = admin[0]?.client_id;

      const checkRoleName: any = await this.callRawQuery(`
        SELECT COUNT(*) as total
        FROM roles r 
        WHERE r.name = '${data?.name}' AND r.deleted_at IS NULL AND r.client_id = '${client_id}'
      `);
      if (checkRoleName[0].total > 0) {
        return this.makeResponse(400, "Role name already exists");
      }

      data.user_type = 'client';
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const roleID: any = uuidv4();
      const saveData = {
        id: roleID,
        name: data?.name,
        details: data?.details || "",
        status: data?.status || 'active',
        client_id: client_id,
        created_at: this.getMySQLDateTime_()
      };
      const result = await this.insertData("roles", saveData);
      /// udate access role rights 
      await this.updateRoleAccessRights(roleID, data.access_rights);
      return this.makeResponse(200, "Role added successfully");

    } catch (error: any) {

      console.log("data role saving error", error)
      return this.makeResponse(500, "Error saving role");
    }
  }

  async updateRole(id: string, data: any) {
    try {

      const admin: any = await this.selectDataQuerySafe("client_logins", { id: data?.userId });
      if (admin.length === 0) {
        return this.makeResponse(400, "Business account not found");
      }


      // restrict default role edit 
      if (id === "0000-0000-0000-0001") {
        return this.makeResponse(400, "Updating default role is disabled");
      }

      const client_id: any = admin[0]?.client_id;
      const savedType: any = await this.callRawQuery(`
        SELECT COUNT(*) as total
        FROM roles r 
        WHERE r.id != '${id}' AND r.name = '${data?.name}' AND r.deleted_at IS NULL
      `);

      if (savedType[0].total > 0) {
        return this.makeResponse(401, "Role name already exists");
      }

      data.user_type = 'client';
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const saveData = {
        name: data?.name,
        details: data?.details,
        status: data?.status,
        updated_at: this.getMySQLDateTime_()
      };
      await this.updateData("roles", `id = '${id}' AND client_id = '${client_id}'`, saveData);
      await this.updateRoleAccessRights(id, data.access_rights);
      return this.makeResponse(200, "Role edited successfully");
    } catch (error: any) {
      return this.makeResponse(500, "Error updating role");
    }
  }

  async deleteRole(id: string) {
    try {
      const role: any = await this.selectDataQuerySafe("roles", { id, deleted_at: null });
      if (role.length === 0) {
        return this.makeResponse(400, "Role not found");
      }

      await this.updateData("roles", `id = '${id}' AND deleted_at IS NULL`, { deleted_at: this.getMySQLDateTime_() });
      const defaultRole: any = await this.defaultRole();
      await this.updateData("client_logins", `role = '${id}'`, { role: defaultRole.id });
      return this.makeResponse(200, "Role deleted successfully");
    } catch (error: any) {
      return this.makeResponse(500, "Error deleting role");
    }
  }

  async getTeams(data: any) {
    try {
      const team: any = await this.callQuerySafe(`
        SELECT u.*, r.name as role_name, r.id as role_id, r.details as role_details, r.status as role_status
         FROM client_logins u   
          LEFT JOIN roles r ON u.role = r.id
           WHERE u.deleted_at IS NULL AND u.client_id = ? ORDER BY u.created_at DESC
      `, [data?.clientId]);


      // remove password from team members
      for (const member of team) {
        delete member.password;
      }
      return this.makeResponse(200, "Teams fetched successfully", team);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching teams");
    }
  }

  async getATeamMember(id: string, data: any) {
    try {
      const admin: any = await this.selectDataQuerySafe("client_logins", { id: data?.userId });
      if (admin.length === 0) {
        return this.makeResponse(400, "Business account not found");
      }
      const client_id: any = admin[0]?.client_id;
      const team: any = await this.callRawQuery(`
        SELECT u.*, r.name as role_name, r.id as role_id, r.details as role_details, r.status as role_status
         FROM client_logins u   
          LEFT JOIN roles r ON u.role = r.id
           WHERE u.deleted_at IS NULL AND id = '${id}' AND u.client_id = '${client_id}' ORDER BY u.created_at DESC
      `);

      delete team[0].password;
      return this.makeResponse(200, "Team member fetched successfully", team[0]);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching team member");
    }
  }

  async addATeamMember(data: any) {
    try {
      const admin: any = await this.selectDataQuerySafe("client_logins", { id: data?.userId });
      if (admin.length === 0) {
        return this.makeResponse(400, "Business account not found");
      }
      const client_id: any = admin[0]?.client_id;
      const { email, first_name, last_name, role_id } = data;
      // Generate a secure password that meets policy requirements
      const password = this.generateSecurePassword(12, true);
      const encryptedPassword = await this.generatePassword(password);

      // check if email exists
      const existingEmail = await this.selectDataQuerySafe("client_logins", { email });
      if (existingEmail.length > 0) {
        return this.makeResponse(400, "Email already exists");
      }

      data.user_type = 'client';
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const adminData = {
        client_id,
        email,
        password: encryptedPassword,
        role: role_id,
        first_name,
        last_name,
        default_password: true
      };
      await this.insertData("client_logins", adminData);
      this.sendEmail("CLIENT_REGISTRATION", email, first_name, password);
      return this.makeResponse(200, "Business team member added successfully");
    } catch (error: any) {
      console.log("error adding client >>>>>>>>>>>>>", error)
      return this.makeResponse(500, "Error adding team member");
    }
  }

  async editATeamMember(id: string, data: any) {
    try {
      const admin: any = await this.selectDataQuerySafe("client_logins", { id: data?.userId });
      if (admin.length === 0) {
        return this.makeResponse(400, "Business account not found");
      }
      const client_id: any = admin[0]?.client_id;

      data.user_type = 'client';
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const existingAdmin: any[] = await this.selectDataQuerySafe("client_logins", { id });
      if (existingAdmin.length === 0) {
        return this.makeResponse(400, "Business admin is not known");
      }

      let saveContactUserData: any = {}
      let password: any;
      if (data?.reset_password && data?.reset_password === true) {
        // Generate a secure password that meets policy requirements
        password = this.generateSecurePassword(12, true);
        const encryptedPassword: any = await this.generatePassword(password);
        saveContactUserData.password = encryptedPassword;
        saveContactUserData.default_password = true;
      }
      saveContactUserData.first_name = data?.first_name;
      saveContactUserData.last_name = data?.last_name;
      saveContactUserData.status = data?.status;
      saveContactUserData.role = data?.role_id;
      await this.updateData("client_logins", `id = '${id}'`, saveContactUserData);
      if (data?.reset_password && data?.reset_password === true) {
        this.sendEmail("CLIENT_PASSWORD_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", password);
      }
      return this.makeResponse(200, "Business team member updated successfully");

    } catch (error: any) {
      console.log("error editing client >>>>>>>>>>>>>", error)
      return this.makeResponse(500, "Error fetching team member");
    }
  }

  async Logs(clientId: any) {
    const response: any = await this.callQuerySafe("select l.*,c.email from operation_logs l INNER JOIN client_logins ON l.client_id = c.client_id where l.client_id=?", [clientId]);
    return this.makeResponse(200, "success", response);

  }


  async deleteCLientMember(id: string) {
    try {

      const role: any = await this.selectDataQuerySafe("client_logins", { id, deleted_at: null });
      if (role.length === 0) {
        return this.makeResponse(400, "Member account not found");
      }
      await this.updateData("client_logins", `id = '${id}' AND deleted_at IS NULL`, { deleted_at: this.getMySQLDateTime_() });
      return this.makeResponse(200, "Business team member deleted successfully");
    } catch (error: any) {
      return this.makeResponse(500, "Error deleting business team member");
    }
  }


  async confirmTeamPasswordReset(id: string, data: any) {
    try {

      const client_id: any = data?.userId;
      data.user_type = 'client';
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const resetRequest: any[] = await this.selectDataQuerySafe("reset_credentials_requests", { id: id, reset_type: 'PASSWORD', used: 0 });
      if (resetRequest.length === 0) {
        return this.makeResponse(400, "Reset request not found");
      }


      const existingAdmin: any[] = await this.selectDataQuerySafe("client_logins", { id: resetRequest[0].user_id });
      if (existingAdmin.length === 0) {
        return this.makeResponse(400, "Business admin is not known");
      }

      let saveContactUserData: any = {}
      let password: any;
      // Generate a secure password that meets policy requirements
      password = this.generateSecurePassword(12, true);
      const encryptedPassword: any = await this.generatePassword(password);
      saveContactUserData.password = encryptedPassword;
      saveContactUserData.default_password = true;

      // update clinet login 
      await this.updateData("client_logins", `id = '${resetRequest[0].user_id}'`, saveContactUserData);

      // update reset credentials requests 
      const saveContactUserData_ = {
        used: 1,
        request_approver_id: data?.userId,
        updated_at: this.getMySQLDateTime_()
      }

      await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND client_id = '${existingAdmin[0].client_id}' AND reset_type = 'PASSWORD'`, saveContactUserData_);
      // send mail
      this.sendEmail("CLIENT_PASSWORD_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", password);
      return this.makeResponse(200, "user password reset email sent successfully");
    } catch (error: any) {
      console.log("Error updating user password", error)
      return this.makeResponse(500, "Error updating user password");
    }
  }


  async cancelTeamPasswordReset(id: string, data: any) {
    try {


      const resetRequest: any[] = await this.selectDataQuerySafe("reset_credentials_requests", { id: id, reset_type: 'PASSWORD', used: 0 });
      if (resetRequest.length === 0) {
        return this.makeResponse(400, "Reset request not found");
      }


      const existingAdmin: any[] = await this.selectDataQuerySafe("client_logins", { id: resetRequest[0].user_id });
      if (existingAdmin.length === 0) {
        return this.makeResponse(400, "Business admin is not known");
      }

      // update reset credentials requests 
      const saveContactUserData_ = {
        used: 1,
        request_approver_id: data?.userId,
        updated_at: this.getMySQLDateTime_()
      }

      await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND client_id = '${existingAdmin[0].client_id}' AND reset_type = 'PASSWORD'`, saveContactUserData_);
      return this.makeResponse(200, "User password reset was cancelled successfully");

    } catch (error: any) {
      console.log("Error cancelling user password reset >>>>>>>>>>>>>", error)
      return this.makeResponse(500, "Error cancelling user password reset");
    }
  }


  async confirmTeam2faReset(id: string, data: any) {
    try {

      const client_id: any = data?.userId;
      data.user_type = 'client';
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }


      const resetRequest: any[] = await this.selectDataQuerySafe("reset_credentials_requests", { id: id, reset_type: '2FA', used: 0 });
      if (resetRequest.length === 0) {
        return this.makeResponse(400, "Reset request not found");
      }

      const existingAdmin: any[] = await this.selectDataQuerySafe("client_logins", { id: resetRequest[0].user_id });
      if (existingAdmin.length === 0) {
        return this.makeResponse(400, "Business admin is not known");
      }

      await this.deleteData("user_2fa", `user_id = '${resetRequest[0].user_id}' AND user_type = 'client'`);
      // update reset credentials requests 
      const saveContactUserData_ = {
        used: 1,
        request_approver_id: data?.userId,
        updated_at: this.getMySQLDateTime_()
      }

      await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND client_id = '${existingAdmin[0].client_id}' AND reset_type = '2FA'`, saveContactUserData_);
      // send mail
      this.sendEmail("CLIENT_2FA_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", "");
      return this.makeResponse(200, "Account 2FA reset has been confirmed successfully");
    } catch (error: any) {
      console.log("Error updating user password", error)
      return this.makeResponse(500, "Error updating 2FA reset");
    }
  }


  async cancelTeam2faReset(id: string, data: any) {
    try {


      const resetRequest: any[] = await this.selectDataQuerySafe("reset_credentials_requests", { id: id, reset_type: '2FA', used: 0 });
      if (resetRequest.length === 0) {
        return this.makeResponse(400, "Reset request not found");
      }

      const existingAdmin: any[] = await this.selectDataQuerySafe("client_logins", { id: resetRequest[0].user_id });
      if (existingAdmin.length === 0) {
        return this.makeResponse(400, "Business admin is not known");
      }

      // update reset credentials requests 
      const saveContactUserData_ = {
        used: 1,
        request_approver_id: data?.userId,
        updated_at: this.getMySQLDateTime_()
      }
      await this.updateData("reset_credentials_requests", `user_id = '${resetRequest[0].user_id}' AND client_id = '${existingAdmin[0].client_id}' AND reset_type = '2FA'`, saveContactUserData_);
      return this.makeResponse(200, "Account 2FA reset has been cancelled successfully");

    } catch (error: any) {
      console.log("Error cancelling user password reset >>>>>>>>>>>>>", error)
      return this.makeResponse(500, "Error cancelling user 2FA reset");
    }
  }
















  async getProducts(data: any) {
    try {

      const { page, size = 8, currency = '', transaction_type = '', status = '', search = '' } = data || {};
      const limit = parseInt(size) > 0 ? parseInt(size) : 8;
      const offset = page ? (Number(page) - 1) * limit : 0;

      // Build where clause dynamically
      let whereClause = "1=1"; // always true

      if (currency && currency.trim() !== "") {
        whereClause += ` AND currency = '${currency}'`;
      }

      if (transaction_type && transaction_type.trim() !== "") {
        whereClause += ` AND transaction_type = '${transaction_type}'`;
      }

      if (status && status.trim() !== "") {
        whereClause += ` AND status = '${status}'`;
      }

      if (search && typeof search === "string" && search.trim() !== "") {
        const searchStr = search.trim().replace(/'/g, "''"); // escape single quotes
        whereClause += ` AND (
          name LIKE '%${searchStr}%' 
          OR description LIKE '%${searchStr}%'
          OR sku LIKE '%${searchStr}%'
        )`;
      }

      // ✅ If no page → fetch all
      if (!page) {
        let products: any = await this.callQuerySafe(`Select * from products where ${whereClause} ORDER BY created_at DESC`) as any[];
        if (!products || products.length === 0) {
          return this.makeResponse(400, "No products found", []);
        }

        // check if tier and map on a tier vollection object 
        products = products.map(async (product: any) => {
          if (product.fee_type === "TIER") {
            product.tier_fees = await this.selectDataQuerySafe("tier_fees", { product_id: product.product_id });

          }
          return product;
        });
        products = await Promise.all(products);
        return this.makeResponse(200, "Products fetched successfully", products);
      }

      // ✅ With pagination
      // Count total
      const countQuery = `SELECT COUNT(*) AS total FROM products WHERE ${whereClause}`;
      const countResult: any = await this.callQuerySafe(countQuery);
      const totalItems = countResult[0]?.total || 0;

      if (totalItems === 0) {
        return this.makeResponse(400, "No products found", {
          items: [],
          pagination: {
            current_page: Number(page),
            total_pages: 0,
            total_items: 0,
            items_per_page: limit,
            hasNextPage: false,
            hasPrevPage: false,
            next_page: null,
            previous_page: null
          }
        });
      }

      const totalPages = Math.ceil(totalItems / limit);

      // Fetch paginated data
      let products: any = await this.callQuerySafe(`Select * from products where ${whereClause} ORDER BY created_at DESC LIMIT ${limit} OFFSET ${offset}`) as any[];
      // check if tier and map on a tier vollection object 
      if (products.length > 0) {
        products = products.map(async (product: any) => {
          if (product.fee_type == "TIER") {
            product.tier_fees = await this.callQuerySafe(`Select * from tier_fees where product_id = ${product.product_id} ORDER BY created_at DESC`) as any[];
          }
          return product;
        });
        products = await Promise.all(products);
      }

      return this.makeResponse(200, "Products fetched successfully", {
        items: products,
        pagination: {
          current_page: Number(page),
          total_pages: totalPages,
          total_items: totalItems,
          items_per_page: limit,
          hasNextPage: Number(page) < totalPages,
          hasPrevPage: Number(page) > 1,
          next_page: Number(page) < totalPages ? Number(page) + 1 : null,
          previous_page: Number(page) > 1 ? Number(page) - 1 : null
        }
      });

    } catch (error: any) {
      console.error("Error fetching products:", error);
      return this.makeResponse(500, "Error fetching products");
    }
  }


  async getSinglePairPrice(data: any) {
    try {


      (data?.amount === undefined || data?.amount == "") ? data.amount = 1 : "";
      const { amount, to, from } = data;
      if (!amount || !from || !to) {
        return this.makeResponse(404, "provide amount, from/to currency to get rate of exchange");
      }

      const prices: any = (await this.callRawQuery(`
      SELECT id, enabled, hasCrypto, referencePrice, base_currency,  quote_currency, pair, markup, markdown, created_at, updated_at 
      FROM exchange_rates WHERE enabled = 1 AND base_currency = '${data.from}' AND quote_currency = '${data.to}'
    `));

      if (prices.length === 0) {
        return this.makeResponse(404, "Exchange pair not found");
      }


      if (prices[0].updated_at) {
        const updatedAt = new Date(prices[0].updated_at);
        const now = new Date();
        const diffInMinutes = (now.getTime() - updatedAt.getTime()) / (1000 * 60);

        if (diffInMinutes < 1) {
          const cachedRate = prices[0].referencePrice || "";
          const fBestExchangeRate = cachedRate ? Number(cachedRate) : "";
          const fBestExchangeRateAmount = fBestExchangeRate ? (fBestExchangeRate * data.amount) : "";

          const result = {
            rate: fBestExchangeRate,
            exchangedAmount: fBestExchangeRateAmount,
            source: "cached"
          };
          return this.makeResponse(200, "Exchange pair amount calculated successfully (cached)", result);
        }
      }

      const rateDetails: any = await ratesService.getRates({ quote: data.to, base: data.from });
      console.log("rateDetails", rateDetails);
      const fBestExchangeRate: any = (rateDetails?.status) ? Number(rateDetails?.price) : "";
      const fBestExchangeRateAmount: any = (fBestExchangeRate * data.amount);

      const result = {
        rate: fBestExchangeRate,
        exchangedAmount: fBestExchangeRateAmount
      };

      return this.makeResponse(200, "Exchange pair amount calculated successfully", result);
    } catch (error: any) {
      console.error("Error fetching app pair price:", error);
      return this.makeResponse(500, "Error fetching app pair prices");
    }
  }


}

export default Accounts;