// test.ts

import kotaniPay from "../helpers/kotani";
import CNGNUtility from "../helpers/CNGNUtility";
import { Network } from "cngn-typescript-library";
import MudaPayment from "../helpers/MudaPayment";
import Accounts from "../models/accounts";
import Quidax from "../helpers/Quidax";
import { WebhookSender } from "../helpers/webhookSender";
import HoneyCoin from "../helpers/HoneyCoin";
import { MudaProvider } from "../helpers/MudaProvider";



 
export async function runTests() {
    // createPaymentIntent();

    MudaPayment.getJWT();
  }
