import RequestHelper from "../helpers/request.helper";
import axios, { AxiosResponse } from "axios";
import dotenv from "dotenv";

dotenv.config();

// Types and interfaces
interface MagmaPayConfig {
  apiUrl: string;
  publicKey: string;
  apiKey: string;
  webhookSecret: string;
}

interface TokenResponse {
  status: "success" | "error";
  token?: string;
  message?: string;
}

export interface PaymentStatus {
  status: "new" | "success" | "pending" | "failed" | "refunded" | "cancelled";   
  message: string;
  data: any;
}

export interface PaymentChannel {
  channel: "mobile_money" | "credit_card" | "wave";  
}

export interface InitiateCollectionData {
  merchant_transaction_id: string;
  amount: number;
  currency: string;
  description?: string;
  payee: string;
  payee_first_name?: string;
  payee_last_name?: string;
  channel: "mobile_money" | "credit_card" | "wave";
  webhook_url?: string;
  success_url?: string;
  error_url?: string;
  custom_field?: string;
}

export interface PaymentProcessData {
  payment_token: string;
  otp_code?: string;
}

export interface InitiatePayoutData {
  merchant_transaction_id: string;
  amount: number;
  currency: string;
  channel: "mobile_money" | "bank_account";
  receiver_first_name: string;
  receiver_last_name: string;

  payment_method: string;
  country_code: string;
  receiver_account: string;

  payee?: string;
  description?: string;
  webhook_url?: string;
  custom_field?: string;
  receiver_account_number?: string;
  receiver_bank_name?: string;
  receiver_bank_short_code?: string;
  sender_firstname?: string;
  sender_lastname?: string;
}

export interface ValidateBankAccountOrPhoneNumberData {
  country_code: string;
  channel: "bank_account" | "mobile_money";
  operator_code: string;
  account_number?: string;
  phone_number?: string;
}

export interface TransactionHistoryData {
  start_date?: string;
  end_date?: string;
  channel?: string;
  currency?: string;
  status?: string;
}

export type PaymentStatusType = PaymentStatus[keyof PaymentStatus];

export type CountryCode = 
  | "BJ"
  | "CI"
  | "TG"
  | "SN"
  | "ML"
  | "BF"
  | "CM"
  | "SL";


// add accespted prefixes   by country code 

export interface AcceptedPrefixes {
  country_code: CountryCode;
  prefixes: string[];
}

// multiple accepted prefixes by country code and add Payment methods	and county name and currency
export interface AcceptedPrefixes {
  country_code: CountryCode;
  prefixes: string[];
  payment_method: string;
  county_name: string;
  currency: string;
}



class MagmaPay {
  private config: MagmaPayConfig;
  private requestHeaders: Record<string, string>;

  constructor() {
    this.config = {
      apiUrl: process.env.MAGMAPAY_API_URL ?? "https://api.magmaonepay.com/v1",
      publicKey: process.env.MAGMAPAY_PUBLIC_KEY ?? "",
      apiKey: process.env.MAGMAPAY_SECRET_KEY ?? "",
      webhookSecret: process.env.MAGMAPAY_WEBHOOK_SECRET ?? ""
    };

    this.requestHeaders = {
      Accept: "application/json",
      "Content-Type": "application/json",
    };
  }



  private async definePrefixList(): Promise<Record<CountryCode, AcceptedPrefixes[]>> {
    return {
      BJ: [
        {
          country_code: "BJ",
          prefixes: [
            "+22999", "+22998", "+22995", "+22994", "+22965", "+22964"
          ],
          payment_method: "MOOV_BJ",
          county_name: "Benin",
          currency: "XOF"
        },
        {
          country_code: "BJ",
          prefixes: [
            "+22942", "+22946", "+22950", "+22951", "+22952", "+22953", "+22954",
            "+22956", "+22957", "+22959", "+22961", "+22962", "+22966", "+22967",
            "+22969", "+22990", "+22991", "+22996", "+22997"
          ],
          payment_method: "MTN_BJ",
          county_name: "Benin",
          currency: "XOF"
        }
      ],
  
      BF: [
        {
          country_code: "BF",
          prefixes: [
            "+22601", "+22602", "+22651", "+22652", "+22653", "+22660", "+22661",
            "+22662", "+22663", "+22670", "+22671", "+22672", "+22673"
          ],
          payment_method: "MOOV_BF",
          county_name: "Burkina Faso",
          currency: "XOF"
        },
        {
          country_code: "BF",
          prefixes: [
            "+22605", "+22606", "+22607", "+22654", "+22655", "+22656", "+22657",
            "+22664", "+22665", "+22666", "+22667", "+22674", "+22675", "+22676", "+22677"
          ],
          payment_method: "ORANGE_BF",
          county_name: "Burkina Faso",
          currency: "XOF"
        }
      ],
  
      CM: [
        {
          country_code: "CM",
          prefixes: [
            "+237655", "+237656", "+237657", "+237658", "+237659"
          ],
          payment_method: "ORANGE_CM",
          county_name: "Cameroun",
          currency: "XAF"
        },
        {
          country_code: "CM",
          prefixes: [
            "+237650", "+237651", "+237652", "+237653", "+237654",
            "+237670", "+237671", "+237672", "+237673", "+237674", "+237675", "+237676",
            "+237677", "+237678", "+237679", "+237680", "+237681", "+237682", "+237683",
            "+237684", "+237685", "+237686", "+237687", "+237688", "+237689"
          ],
          payment_method: "MTN_CM",
          county_name: "Cameroun",
          currency: "XAF"
        }
      ],
  
      CI: [
        {
          country_code: "CI",
          prefixes: ["+22501"],
          payment_method: "MOOV_CI",
          county_name: "Côte d'Ivoire",
          currency: "XOF"
        },
        {
          country_code: "CI",
          prefixes: ["+22505"],
          payment_method: "MTN_CI",
          county_name: "Côte d'Ivoire",
          currency: "XOF"
        },
        {
          country_code: "CI",
          prefixes: ["+22507"],
          payment_method: "ORANGE_CI",
          county_name: "Côte d'Ivoire",
          currency: "XOF"
        }
      ],  
      ML: [
        {
          country_code: "ML",
          prefixes: [
            "+2237", "+22382", "+22383", "+22384", "+223850", "+223851", "+223852",
            "+223853", "+223854", "+22390", "+22391", "+22392", "+22393", "+22394"
          ],
          payment_method: "ORANGE_ML",
          county_name: "Mali",
          currency: "XOF"
        },
        {
          country_code: "ML",
          prefixes: [
            "+2236", "+22389", "+22395", "+22396", "+22397", "+22398", "+22399"
          ],
          payment_method: "MOOV_ML",
          county_name: "Mali",
          currency: "XOF"
        }
      ],
  
      SN: [
        {
          country_code: "SN",
          prefixes: ["+22177", "+22178"],
          payment_method: "ORANGE_SN",
          county_name: "Senegal",
          currency: "XOF"
        },
        {
          country_code: "SN",
          prefixes: ["+22176"],
          payment_method: "FREE_SN",
          county_name: "Senegal",
          currency: "XOF"
        },
        {
          country_code: "SN",
          prefixes: ["+22170", "+22177", "+22176", "+22178"],
          payment_method: "EXPRESSO_SN",
          county_name: "Senegal",
          currency: "XOF"
        },
        {
          country_code: "SN",
          prefixes: ["+22170", "+22175"],
          payment_method: "WAVE_SN",
          county_name: "Senegal",
          currency: "XOF"
        }
      ],
      TG: [
        {
          country_code: "TG",
          prefixes: ["+22870", "+22873", "+22890", "+22891", "+22892", "+22893"],
          payment_method: "TMONEY_TG",
          county_name: "Togo",
          currency: "XOF"
        },
        {
          country_code: "TG",
          prefixes: ["+22879", "+22899", "+22898", "+22896", "+22897"],
          payment_method: "MOOV_TG",
          county_name: "Togo",
          currency: "XOF"
        }
      ],
  
      SL: [
        {
          country_code: "SL",
          prefixes: ["+23274", "+23275"],
          payment_method: "ORANGE_SL",
          county_name: "Sierra Leone",
          currency: "SLL"
        }
      ]
    };
  }
  

  private async makeRequest(
    method: "get" | "post" | "put",
    endpoint: string,
    data: any = {}
  ): Promise<any> {
    try {
      
      const fullUrl = `${this.config.apiUrl}${endpoint}`.replace(/\s/g, "");
      console.log("🔹 Full URL:", fullUrl);

      await RequestHelper.setEndpoint(fullUrl);
      const authToken = this.config.apiKey;

      if (authToken === "" || !authToken) {
        throw new Error("Failed to generate MagmaOnePay token");
      }

      this.requestHeaders["Authorization"] = `Bearer ${authToken}`;
      await RequestHelper.setHeaders(this.requestHeaders);

      if (data && Object.keys(data).length > 0) {
        await RequestHelper.setData(data);
      }

      await RequestHelper[`${method}Request`]();
      const errors = await RequestHelper.getErrors();
      const responseData = await RequestHelper.getResults();
      console.log("responseData  VVVVVVV", responseData)
      return responseData?.code === 200 ? responseData : responseData;

    } catch (error: any) {
      console.error(`Request error for ${method.toUpperCase()} ${endpoint}:`, error);
      throw new Error(error.message || "Request failed");
    }
  }

  // Collection Methodsxx
  public async getCollectionPaymentMethods() {
    const response = await this.makeRequest("get", "/misc/payin/services");
    return response.data;
  }

  public async initiateCollection(data: InitiateCollectionData) {
    const response = await this.makeRequest("post", "/payment/init", data);
    // console.log("🔹 Initiating magma pay collection:", data, response);
    return response?.data;
  }

  public async paymentProcess(data: PaymentProcessData) {
    const response = await this.makeRequest("post", "/payment/process", data);
    console.log("🔹 Processing magma pay collection:", data, response);
    return response?.data;
  }

  public async getCollectionStatus(transactionId: string) {
    const response = await this.makeRequest("get", `/payment/${transactionId}`);
    return response?.data;
  }

  // Payout Methods
  public async getPayoutPaymentMethods() {
    const response = await this.makeRequest("get", "/misc/payout/services");
    return response.data;
  }

  public async getWalletBalances() {
    const response = await this.makeRequest("get", "/misc/balance");
    return response.data;
  }


  public async getBalanceByCountryPrefix(countryCode: string, currencyCode: string) {

    try{

        const allBalances      = await this.getWalletBalances();
        const accBalance: any  = allBalances.data.filter((balance: any) => balance.code === countryCode && balance.currency === currencyCode.toUpperCase())
        console.log("balance block", accBalance)
        if(accBalance.length > 0) {
          console.log("avaliable balance",  countryCode, currencyCode, accBalance[0]?.balance_available)
          return accBalance[0]?.balance_available || 0
        } else {
          return 0
        }
   
    } catch (error: any){
      
       console.log("error getting magma available balance ", error)
       return 0;  
    }
  }

  public async initiatePayout(data: InitiatePayoutData) {
    const response = await this.makeRequest("post", "/payout/transfer", data);
    console.log("🔹 Initiating magma pay payout:", data, response);
    return response.data;
  }

  public async getPayoutStatus(transferToken: string){
    return this.makeRequest("get", `/payout/transfer/${transferToken}`);
  }

  public async getPayoutStatusbyReference(payoutId: string) {
    return this.makeRequest("get", `/payout/transfer/reference/${payoutId}`);
  }

  // Validation Methods
  public async validateBankAccountOrPhoneNumber(data: ValidateBankAccountOrPhoneNumberData) {
    if (data.channel === "bank_account" && !data.account_number) {
      throw new Error("account_number is required for bank_account channel");
    }
    
    if (data.channel === "mobile_money" && !data.phone_number) {
      throw new Error("phone_number is required for mobile_money channel");
    }

    let sendData: ValidateBankAccountOrPhoneNumberData;
    if(data.channel === "mobile_money"){
      sendData = {
          country_code: data.country_code,
          channel: data.channel,
          operator_code: data.operator_code,
          phone_number: data.phone_number,
        }
    } else {

      sendData = {
        country_code: data.country_code,
        channel: data.channel,
        operator_code: data.operator_code,
        account_number: data.account_number
      }
    }

    const response = await this.makeRequest("post", "/misc/check-account", sendData);
    console.log("🔹 Validating magma pay bank account or phone number:", data, sendData, response);
    return response.data;
  }

  public validatePayeePhoneNumber(phoneNumber: string, paymentServiceCode?: CountryCode): {
    isValid: boolean;
    message: string;
    formattedNumber?: string;
    prefix?: string | null;
  } {

      const phoneRegexMap: Record<CountryCode, RegExp> = {
        BJ: /^(?:\+?229)/,
        CI: /^(?:\+?225)/,
        TG: /^(?:\+?228)/,
        SN: /^(?:\+?221)/,
        ML: /^(?:\+?223)/,
        BF: /^(?:\+?226)/,
        // TD: /^(?:\+?235)/,
        CM: /^(?:\+?237)/,
        SL: /^(?:\+?232)/,
      };
    

    
    const cleanNumber = phoneNumber.trim();
    if (!cleanNumber) {
      return {
        isValid: false,
        message: "Phone number is required"
      };
    }

    const e164Regex = /^\+[1-9]\d{4,14}$/;
    if (!e164Regex.test(cleanNumber)) {
      return {
        isValid: false,
        message: "Phone number must be in E.164 format (+[country code][phone number]). Example: +2250102030405"
      };
    }

    const digitsOnly = cleanNumber.substring(1);
    if (digitsOnly.length < 5 || digitsOnly.length > 15) {
      return {
        isValid: false,
        message: "Phone number must be between 5 and 15 digits (excluding country code)"
      };
    }

    const countryCodeMatch = cleanNumber.match(/^\+([1-9]\d{0,2})/);
    if (!countryCodeMatch) {
      return {
        isValid: false,
        message: "Invalid country code. Must be 1-3 digits and not start with 0"
      };
    }

    if(paymentServiceCode){


      const paymentServiceCode_ = paymentServiceCode.split("_")[1] as CountryCode;
      const regex = phoneRegexMap[paymentServiceCode_];
      if (!regex) {
        return {
          isValid: false,
          message: "Phone number is invalid"
        };
      }
  

      if (!regex.test(phoneNumber.replace(/\s+/g, ""))) {
        return {
          isValid: false,
          message: `Phone number is invalid for the selected payment provider`
        };
      }
      
  
      const matchCode = cleanNumber.match(phoneRegexMap[paymentServiceCode_]); 
      return {
        message: "Phone number is valid",
        prefix: matchCode ? matchCode[0] : null,  // the matched prefix string
        isValid: !!matchCode 
      };


    } else {
      return {
        isValid: false,
        message: `Unknown payment service code`
      };
    } 
  }



  public async validateAcceptedCurrency(currency: string) {
    return ["UGX", "KES", "NGN", "GHS", "XOF"].includes(currency);
  }


  public async validateChannelUsed(channel: PaymentChannel) {
    return ["mobile_money", "credit_card", "wave"].includes(channel.channel);
  }

  // Transaction History Methods
  public async getTransactionHistory(queryData?: TransactionHistoryData) {
    const queryParams = new URLSearchParams();
        
    if (queryData?.start_date) {
      queryParams.append('start_date', queryData.start_date);
    }
    if (queryData?.end_date) {
      queryParams.append('end_date', queryData.end_date);
    }
    if (queryData?.channel) {
      queryParams.append('channel', queryData.channel);
    }
    if (queryData?.currency) {
      queryParams.append('currency', queryData.currency);
    }
    if (queryData?.status) {
      queryParams.append('status', queryData.status);
    }

    const queryString = queryParams.toString();
    const endpoint = queryString ? `/payout/transfer/history?${queryString}` : '/payout/transfer/history';
    return this.makeRequest("get", endpoint);
  }

  public async getTransactionHistoryByAccount(receiverAccount: string) {
    return this.makeRequest("get", `/payout/transfer/history/${receiverAccount}`);
  }

  // Utility Methods
  public generateUniqueReference(): string {
    return `MAGMAPAY_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 10)}`;
  }


  // search for supported prefixes by country code and return payment method
  public async validatePhonePrefix(phoneNumber: string, country_code: CountryCode) {
    const prefixList = await this.definePrefixList();
    const paymentMethod = prefixList[country_code].find(p => p.prefixes.some(prefix => phoneNumber.startsWith(prefix)));
    return paymentMethod ? paymentMethod.payment_method : false;
  }
}

export default MagmaPay;