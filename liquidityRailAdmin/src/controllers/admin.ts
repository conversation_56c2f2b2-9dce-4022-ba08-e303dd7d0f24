import express, { Request, Response } from "express";
import Accounts from "../models/admin";
import { JWTMiddleware } from '../helpers/jwt.middleware';

// type Currency = 'USD' | 'UGX' | 'KES' | 'NGN' | 'GHS' | 'EUR' | 'GBP';
// type SearchQuery = {
//   value: string;
//   type: 'transaction' | 'client' | 'provider' | 'service';
// };

const ROUTER = express.Router();
const accounts = new Accounts();

// Middleware to apply JWT verification conditionally
const APPLY_JWT_CONDITIONALLY = (req: Request, res: Response, next: any) => {
  const exemptedRoutes = ["login"];
  if (!exemptedRoutes.includes(req.path.split('/')[1])) {
    // Apply JWT verification
    // Assuming JWTMiddleware.verifyToken is a static method
    JWTMiddleware.verifyToken(req, res, next);
    // Instead of the above, you can directly use JWTMiddleware.verifyToken(req, res, next);
    //next();
  } else {
    next();
  }
};



/**
 * ============================================================
 * PUBLIC ADMIN ENDPOINTS - LIMITED THIRD PARTY ACCESS
 * ============================================================
 * These endpoints can be shared with trusted third parties
 * who need limited administrative capabilities
 */


// liquidyt rails reports
ROUTER.get("/reports/rails/transactions", getRailsTransactionsStats);
ROUTER.get("/reports/rails/transaction/fees",  getRailsTransactionsFeesStats);
ROUTER.get("/reports/rails/clients",  getRailsClientsStats);
ROUTER.get("/reports/rails/clients/trades",  getRailsClientsTradesStats);
ROUTER.get("/reports/rails/providers", getRailsProvidersStats);
ROUTER.get("/reports/rails/services", getRailsServicesStats);
ROUTER.get("/reports/rails/profits", getRailsTransactionsProfits);

// API routes for external services
ROUTER.post("/transaction-quotes/check", checkTransactionQuote);

ROUTER.get("/providers/", APPLY_JWT_CONDITIONALLY, allProviders);
ROUTER.get("/providers/:id/fees", APPLY_JWT_CONDITIONALLY,  providersFees);
ROUTER.get("/providers/:id/fees/:provider_service_id", APPLY_JWT_CONDITIONALLY, providersAFee);
ROUTER.put("/providers/:id/fees/:provider_service_id", APPLY_JWT_CONDITIONALLY, getRailsTransactionsProfits);
ROUTER.get("/providers/:id/charges", APPLY_JWT_CONDITIONALLY, getCharges);

ROUTER.put("/updateLRCharges", APPLY_JWT_CONDITIONALLY, updateCharge);
ROUTER.get("/getCharges", APPLY_JWT_CONDITIONALLY, getCharges); 

ROUTER.get("/charges",  APPLY_JWT_CONDITIONALLY, getTransactionCharges);
ROUTER.put("/charges/:id", APPLY_JWT_CONDITIONALLY, updateTransactionCharges); 
ROUTER.put("/charges/:id/update", APPLY_JWT_CONDITIONALLY, updateTransactionCharges); 

async function updateproviderAddress(req: Request, res: Response) {
  try {
    const result = await accounts.updateproviderAddress(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function deactivateAddress(req: Request, res: Response) {
  try {
    const result = await accounts.deactivateAddress(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


async function getTransactionCharges(req: Request, res: Response) {
  try {
    const PAGE: number = Number(req.query.page) || 1;
    const limit = req.query.limit || 14;
    const SEARCH: any = req.query.search || '';
    const SORT_KEY = req.query.sort_key || '';
    const SORT_ORDER = req.query.sort_order || 'DESC';

    req.body.page = PAGE;
    req.body.limit = limit;
    req.body.search = SEARCH;
    req.body.sort_key = SORT_KEY;
    req.body.sort_order = SORT_ORDER;
    const result = await accounts.getAllTransactionCharges(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error getting charges', error });
  }
}


async function updateTransactionCharges(req: Request, res: Response) {
  try {
    const result = await accounts.updateTransactionCharges(req.params.id, req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error updating charges', error });
  }  
}



async function getCharges(req: Request, res: Response) {
  try {
    const result = await accounts.getProviderCharges({clientId: req.params.id});
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching provider charges', error });
  }
}

async function updateCharge(req: Request, res: Response) {
  try {
    const result = await accounts.updateProviderCharge({
      clientId: req.params.id,
      chargeId: req.params.charge_id,
      ...req.body
    });
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error updating provider charge', error });
  }
}


async function allProviders(req: Request, res: Response) {
  try {
    const PAGE: number = Number(req.query.page) || 1;
    const limit = req.query.limit || 14;
    const SEARCH: any = req.query.search || '';
    const SORT_KEY = req.query.sort_key || '';
    const SORT_ORDER = req.query.sort_order || 'DESC';
    req.body.page = PAGE;
    req.body.limit = limit;
    req.body.search = SEARCH;
    req.body.sort_key = SORT_KEY;
    req.body.sort_order = SORT_ORDER;
    const result = await accounts.getAllProviders(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


async function providersFees(req: Request, res: Response) {
  try {

    req.body.clientId = req.params.id;
    const result = await accounts.getProviderFees(req);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function providersAFee(req: Request, res: Response) {
  try {
    
    req.body.clientId = req.params.id;
    req.body.providerFeeId = req.params.provider_service_id;
    const result = await accounts.getProviderAFee(req);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


async function getRailsTransactionsProfits(req: Request, res: Response) {
  try {
    const PAGE: number = Number(req.query.page) || 1;
    const limit = req.query.limit || 14;
    const SEARCH: any = req.query.search || '';
    const CURRENCY = req.query.currency || '';
    const PAY_IN_STATUS = req.query.pay_in_status ? (Array.isArray(req.query.pay_in_status) ? req.query.pay_in_status : [req.query.pay_in_status]) : ['SUCCESSFUL', 'PENDING'];
    const STATUS = req.query.status ? (Array.isArray(req.query.status) ? req.query.status : [req.query.status]) : [];
    const PROVIDER_ID = req.query.provider_id ? (Array.isArray(req.query.provider_id) ? req.query.provider_id : [req.query.provider_id]) : [];
    const SEND_ASSET = req.query.send_asset ? (Array.isArray(req.query.send_asset) ? req.query.send_asset : [req.query.send_asset]) : [];
    const RECEIVE_CURRENCY = req.query.receive_currency ? (Array.isArray(req.query.receive_currency) ? req.query.receive_currency : [req.query.receive_currency]) : [];
    const START_DATE = req.query.start_date || '';
    const END_DATE = req.query.end_date || '';
    const SORT_KEY = req.query.sort_key || '';
    const SORT_ORDER = req.query.sort_order || 'DESC';

    req.body.page = PAGE;
    req.body.limit = limit;
    req.body.search = SEARCH;
    req.body.currency = CURRENCY;
    req.body.pay_in_status = PAY_IN_STATUS;
    req.body.status = STATUS;
    req.body.provider_id = PROVIDER_ID;
    req.body.send_asset = SEND_ASSET;
    req.body.receive_currency = RECEIVE_CURRENCY;
    req.body.start_date = START_DATE;
    req.body.end_date = END_DATE;
    req.body.sort_key = SORT_KEY;
    req.body.sort_order = SORT_ORDER;
    const RESULT: any = await accounts.getAllQuotesProfits(req.body);
    res.status(RESULT.status).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getRailsTransactionsStats(req: Request, res: Response) {
  try {
    const PAGE: number = Number(req.query.page) || 1;
    const limit = req.query.limit || 14;
    const SEARCH: any = req.query.search || '';
    const CURRENCY = req.query.currency || '';
    const PAY_IN_STATUS = req.query.pay_in_status ? (Array.isArray(req.query.pay_in_status) ? req.query.pay_in_status : [req.query.pay_in_status]) : ['SUCCESSFUL', 'PENDING'];
    const STATUS = req.query.status ? (Array.isArray(req.query.status) ? req.query.status : [req.query.status]) : [];
    const PROVIDER_ID = req.query.provider_id ? (Array.isArray(req.query.provider_id) ? req.query.provider_id : [req.query.provider_id]) : [];
    const SEND_ASSET = req.query.send_asset ? (Array.isArray(req.query.send_asset) ? req.query.send_asset : [req.query.send_asset]) : [];
    const RECEIVE_CURRENCY = req.query.receive_currency ? (Array.isArray(req.query.receive_currency) ? req.query.receive_currency : [req.query.receive_currency]) : [];
    const START_DATE = req.query.start_date || '';
    const END_DATE = req.query.end_date || '';
    const SORT_KEY = req.query.sort_key || '';
    const SORT_ORDER = req.query.sort_order || 'DESC';

    req.body.page = PAGE;
    req.body.limit = limit;
    req.body.search = SEARCH;
    req.body.currency = CURRENCY;
    req.body.pay_in_status = PAY_IN_STATUS;
    req.body.status = STATUS;
    req.body.provider_id = PROVIDER_ID;
    req.body.send_asset = SEND_ASSET;
    req.body.receive_currency = RECEIVE_CURRENCY;
    req.body.start_date = START_DATE;
    req.body.end_date = END_DATE;
    req.body.sort_key = SORT_KEY;
    req.body.sort_order = SORT_ORDER;
    const RESULT: any = await accounts.getAllQuotes(req.body);
    res.status(RESULT.status).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


/**
 * Check if transaction quote exists for auto-swap
 */
async function checkTransactionQuote(req: Request, res: Response) {
  try {
    const { clientId, fromAsset, toAsset } = req.body;

    if (!clientId || !fromAsset || !toAsset) {
      return res.status(400).json({
        status: 400,
        message: 'Missing required fields: clientId, fromAsset, toAsset',
        data: null
      });
    }

    // Check if quote exists in the database
    const quoteExists = await accounts.checkTransactionQuoteExists({
      clientId,
      fromAsset,
      toAsset
    });

    return res.status(200).json({
      status: 200,
      message: 'Transaction quote check completed',
      data: {
        exists: quoteExists,
        clientId,
        fromAsset,
        toAsset
      }
    });

  } catch (error: any) {
    console.error('❌ Error checking transaction quote:', error);
    return res.status(500).json({
      status: 500,
      message: 'Internal server error',
      data: { error: error.message }
    });
  }
}

async function getRailsTransactionsFeesStats(req: Request, res: Response) {
  try {

    const PAGE: number = Number(req.query.page) || 1;
    const limit = req.query.limit || 14;
    const SEARCH: any = req.query.search || '';
    const CURRENCY = req.query.currency || '';
    const PAY_IN_STATUS = req.query.pay_in_status ? (Array.isArray(req.query.pay_in_status) ? req.query.pay_in_status : [req.query.pay_in_status]) : ['SUCCESSFUL', 'PENDING'];
    const STATUS = req.query.status ? (Array.isArray(req.query.status) ? req.query.status : [req.query.status]) : [];
    const PROVIDER_ID = req.query.provider_id ? (Array.isArray(req.query.provider_id) ? req.query.provider_id : [req.query.provider_id]) : [];
    const SEND_ASSET = req.query.send_asset ? (Array.isArray(req.query.send_asset) ? req.query.send_asset : [req.query.send_asset]) : [];
    const RECEIVE_CURRENCY = req.query.receive_currency ? (Array.isArray(req.query.receive_currency) ? req.query.receive_currency : [req.query.receive_currency]) : [];
    const START_DATE = req.query.start_date || '';
    const END_DATE = req.query.end_date || '';
    const SORT_KEY = req.query.sort_key || '';
    const SORT_ORDER = req.query.sort_order || 'DESC';

    req.body.page = PAGE;
    req.body.limit = limit;
    req.body.search = SEARCH;
    req.body.currency = CURRENCY;
    req.body.pay_in_status = PAY_IN_STATUS;
    req.body.status = STATUS;
    req.body.provider_id = PROVIDER_ID;
    req.body.send_asset = SEND_ASSET;
    req.body.receive_currency = RECEIVE_CURRENCY;
    req.body.start_date = START_DATE;
    req.body.end_date = END_DATE;
    req.body.sort_key = SORT_KEY;
    req.body.sort_order = SORT_ORDER;

    const RESULT: any = await accounts.getAllTransactionFees(req.body);
    res.status(RESULT.status).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getRailsClientsStats(req: Request, res: Response) {
  try {
    const PAGE: number = Number(req.query.page) || 1;
    const limit = req.query.limit || 14;
    const SEARCH: any = req.query.search || '';

    req.body.page = PAGE;
    req.body.limit = limit;
    req.body.search = SEARCH;
    const RESULT: any = await accounts.getAllClients(req.body);
    res.status(RESULT.status).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getRailsClientsTradesStats(req: Request, res: Response) {
  try {
    const PAGE: number = Number(req.query.page) || 1;
    const limit = req.query.limit || 14;
    const SEARCH: any = req.query.search || '';

    req.body.page = PAGE;
    req.body.limit = limit;
    req.body.search = SEARCH;
    const RESULT: any = await accounts.getAllClientsTrades(req.body);
    res.status(RESULT.status).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}



async function getRailsProvidersStats(req: Request, res: Response) {
  try {
    const PAGE: number = Number(req.query.page) || 1;
    const limit = req.query.limit || 14;
    const SEARCH: any = req.query.search || '';

    req.body.page = PAGE;
    req.body.limit = limit;
    req.body.search = SEARCH;
    const RESULT: any = await accounts.getAllProviders(req.body);
    res.status(RESULT.status).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getRailsServicesStats(req: Request, res: Response) {
  try {
    const PAGE: number = Number(req.query.page) || 1;
    const limit = req.query.limit || 14;
    const SEARCH: any = req.query.search || '';
    const CURRENCY: any = req.query.currency || '';

    req.body.page = PAGE;
    req.body.limit = limit;
    req.body.search = SEARCH;
    req.body.currency = CURRENCY;
    
    const RESULT: any = await accounts.getRailsServices(req.body);
    res.status(RESULT.status).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

export default ROUTER;