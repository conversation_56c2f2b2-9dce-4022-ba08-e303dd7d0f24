import express, { Request, Response } from 'express';
import {
    createOffer,
    getOrderBook as getOrderBook<PERSON>ontroller,
    cancelOrder,
    getTradingHistory,
    healthCheck,
    getOrderById,
    getTradingBalance,
    getClientBalances,
    getOrderbookPrice,
    getAllOrders,
    cancelAllOrders,
    attachClientToOrder,
    cacheOffers,
    getTradingPairs,
    preflightMarketOrder,
    createPendingRailOrder,
    getPendingRailOrders,
    triggerOrderMatching,
    makePathPayment,
    getTradingAccountPublicKey,
    getTradingAccountBalances,
} from '../controllers/trading';
import { JWTMiddleware } from '../helpers/jwt.middleware';

const router = express.Router();

const applyJWTConditionally = (req: Request, res: Response, next: any) => {
    JWTMiddleware.verifyTokenAccess(req, res, next);
};

// Health check
router.get('/health', healthCheck);

// Balance and pricing
router.get('/balance', getTradingBalance);
router.get('/balances/:clientId', getClientBalances);
router.get('/pairs', getTradingPairs);
router.post('/price', getOrderbookPrice);
router.post('/preflight', preflightMarketOrder);

// Order management
router.post('/orders', createOffer);
router.post('/path-payment', makePathPayment);
router.post('/orders/cancel', cancelOrder);
router.post('/orders/cancel-all/:clientId', cancelAllOrders);
router.post('/orders/attach-client', attachClientToOrder);
router.post('/orders/cache-offers', cacheOffers);
router.get('/orders/history', getTradingHistory);
router.get('/orders/all/:clientId', getAllOrders);
router.get('/orders/:orderId', getOrderById);
router.get('/orderbook/:baseAsset?/:counterAsset?', getOrderBookController);

// Stellar trades endpoint (separate from orders)
router.get('/trades/:clientId', getAllOrders);

// Rail orders
router.post('/rail-orders',  createPendingRailOrder);
router.get('/rail-orders',  getPendingRailOrders);

// Order matching (admin/testing)
router.post('/match-orders', triggerOrderMatching);

// Trading account
router.get('/account', applyJWTConditionally, getTradingAccountPublicKey);
router.get('/account/balances', applyJWTConditionally, getTradingAccountBalances);

export default router; 