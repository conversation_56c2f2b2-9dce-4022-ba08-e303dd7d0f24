-- Add receivable_amount column to stellar_orders table
-- This column stores what the user expects to receive from the trade

ALTER TABLE stellar_orders 
ADD COLUMN receivable_amount DECIMAL(20,8) NULL COMMENT 'Amount of buying_asset that user expects to receive' 
AFTER transfer_amount;

-- Update existing records to populate receivable_amount
-- CORRECTED: amount is always the base asset amount
-- receivable_amount should be the counter asset amount (amount * price)

UPDATE stellar_orders 
SET receivable_amount = ROUND(amount * price, 8)
WHERE receivable_amount IS NULL;

-- Make the column NOT NULL after populating existing data
ALTER TABLE stellar_orders 
MODIFY COLUMN receivable_amount DECIMAL(20,8) NOT NULL COMMENT 'Amount of buying_asset that user expects to receive';
