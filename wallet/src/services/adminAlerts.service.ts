import EmailSender from '../helpers/email';
import BaseModel from '../helpers/base.model';

const mailer = new EmailSender();

export class AdminAlertsService extends BaseModel {
    private adminEmailsCache: string[] = [];
    private thresholdsCache: { [key: string]: number } = {};
    private lastEmailFetchTime: number = 0;
    private lastThresholdFetchTime: number = 0;
    private CACHE_DURATION = 60000; // 1 minute cache

    constructor() {
        super();
    }

    // Fetch admin emails from database
    private async getAdminEmails(alertType?: 'balance' | 'error'): Promise<string[]> {
        // Check if cache is still valid
        const now = Date.now();
        if (this.adminEmailsCache.length > 0 && (now - this.lastEmailFetchTime) < this.CACHE_DURATION) {
            return this.adminEmailsCache;
        }

        try {
            // Build query conditions
            const conditions: any = { is_active: true };
            
            // Filter by alert type preference
            if (alertType === 'balance') {
                conditions.receive_balance_alerts = true;
            } else if (alertType === 'error') {
                conditions.receive_error_alerts = true;
            }

            // Fetch admin emails from database
            const admins = await this.selectDataQuerySafe("admin_emails", conditions, 1000);
            
            if (admins.length > 0) {
                this.adminEmailsCache = admins.map((admin: any) => admin.email);
                this.lastEmailFetchTime = now;
                return this.adminEmailsCache;
            }
        } catch (error: any) {
            console.error('Error fetching admin emails from database:', error);
        }

        // Fallback to default emails if database fetch fails
        return ["<EMAIL>", "<EMAIL>"];
    }

    // Balance threshold configuration - fetch from database
    private async getBalanceThresholds() {
        // Check if cache is still valid
        const now = Date.now();
        if (Object.keys(this.thresholdsCache).length > 0 && (now - this.lastThresholdFetchTime) < this.CACHE_DURATION) {
            return this.thresholdsCache;
        }

        try {
            // Fetch thresholds from database
            const thresholds = await this.selectDataQuerySafe("balance_thresholds", {}, 1000);
            
            if (thresholds.length > 0) {
                // Convert array to object { currency: threshold }
                const thresholdsMap: { [key: string]: number } = {};
                thresholds.forEach((t: any) => {
                    thresholdsMap[t.currency] = parseFloat(t.threshold_amount || '0');
                });
                
                this.thresholdsCache = thresholdsMap;
                this.lastThresholdFetchTime = now;
                return thresholdsMap;
            }
        } catch (error: any) {
            console.error('Error fetching thresholds from database:', error);
        }

        // Fallback to default thresholds if database fetch fails
        return {
            'USD': 1000,
            'USDC': 1000,
            'USDT': 1000,
            'KES': 100000,
            'UGX': 3000000,
            'TZS': 2000000,
            'XLM': 1000,
            'EUR': 800,
            'GBP': 700
        };
    }

    sendDirectEmail(email: string, subject: string, message: string) {
        mailer.sendMail(email, subject, subject, message);
    }

    async sendAdminAlert(type: 'balance' | 'error' | 'threshold', payload: any) {
        let subject = "";
        let message = "";

        if (type === 'balance') {
            subject = "📊 Daily Balances Alert";

            const tableRows = payload.map((b: any) => `
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd;">${b.account || b.name || "-"}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${b.currency || "USD"}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${b.balance?.toLocaleString() || "0.00"}</td>
                </tr>
            `).join("");

            message = `
                <div style="font-family: Arial, sans-serif; line-height: 1.6;">
                    <h3>Daily Balances Summary</h3>
                    <p>Hello,</p>
                    <p>Here's the summary of today's balances:</p>
                    <table style="border-collapse: collapse; width: 100%; margin-top: 10px;">
                        <thead>
                            <tr style="background-color: #f4f4f4;">
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Account</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Currency</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Balance</th>
                            </tr>
                        </thead>
                        <tbody>${tableRows}</tbody>
                    </table>
                    <p style="margin-top: 20px;">Regards,<br/>System Alert Service</p>
                </div>
            `;

        } else if (type === 'threshold') {
            subject = "⚠️ Low Balance Alert";

            const tableRows = payload.map((b: any) => `
                <tr style="background-color: #fff3cd;">
                    <td style="padding: 8px; border: 1px solid #ddd;">${b.account || b.name || "-"}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${b.currency || "USD"}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${b.balance?.toLocaleString() || "0.00"}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${b.threshold?.toLocaleString() || "0.00"}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: #dc3545; font-weight: bold;">⚠️ LOW</td>
                </tr>
            `).join("");

            message = `
                <div style="font-family: Arial, sans-serif; line-height: 1.6;">
                    <h3 style="color: #dc3545;">⚠️ Low Balance Alert</h3>
                    <p>Hello,</p>
                    <p>The following accounts have balances below their thresholds:</p>
                    <table style="border-collapse: collapse; width: 100%; margin-top: 10px;">
                        <thead>
                            <tr style="background-color: #f4f4f4;">
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Account</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Currency</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Current Balance</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">Threshold</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: center;">Status</th>
                            </tr>
                        </thead>
                        <tbody>${tableRows}</tbody>
                    </table>
                    <p style="margin-top: 20px; color: #dc3545; font-weight: bold;">Please take action to replenish these accounts.</p>
                    <p>Regards,<br/>System Alert Service</p>
                </div>
            `;

        } else if (type === 'error') {
            subject = "🚨 System Error Alert";

            message = `
                <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #222;">
                    <h3 style="color: #dc3545;">System Error Notification</h3>
                    <p><strong>Message:</strong> ${payload.message || "Unknown error"}</p>
                    <p><strong>Service:</strong> ${payload.service || "N/A"}</p>
                    <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
                    <pre style="background-color:#f9f9f9;border-left:4px solid #e74c3c;padding:10px;border-radius:4px;white-space:pre-wrap;">
${payload.stack || JSON.stringify(payload, null, 2)}
                    </pre>
                    <p>Regards,<br/>System Alert Service</p>
                </div>
            `;
        }

        // Get admin emails from database
        const adminEmails = await this.getAdminEmails(type === 'threshold' ? 'balance' : type);

        // Send to all admins
        for (const email of adminEmails) {
            await this.sendDirectEmail(email, subject, message);
        }

        return true;
    }

    async checkBalanceThresholds(balances: any[]) {
        const thresholds = await this.getBalanceThresholds();
        const lowBalances = [];

        for (const balance of balances) {
            const currency = balance.currency || balance.asset_code;
            const threshold = thresholds[currency as keyof typeof thresholds];
            const currentBalance = parseFloat(balance.balance || '0');

            if (threshold && currentBalance < threshold) {
                lowBalances.push({
                    account: balance.account || balance.name || 'Unknown',
                    currency: currency,
                    balance: currentBalance,
                    threshold: threshold,
                    status: 'LOW'
                });
            }
        }

        if (lowBalances.length > 0) {
            await this.sendAdminAlert('threshold', lowBalances);
        }

        return lowBalances;
    }

    async sendErrorAlert(error: any, service: string = 'wallet') {
        try {
            await this.sendAdminAlert('error', {
                message: error.message || 'Unknown error',
                service: service,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });
            return true;
        } catch (alertError) {
            console.error('Failed to send error alert:', alertError);
            return false;
        }
    }
}

// Export singleton instance
export const adminAlertsService = new AdminAlertsService();

