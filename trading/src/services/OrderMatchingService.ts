import StellarTrading from '../models/StellarTrading';
import * as cron from 'node-cron';

class OrderMatchingService {
    private stellarTrading: StellarTrading;
    private isRunning: boolean = false;

    constructor() {
        this.stellarTrading = new StellarTrading();
    }

    /**
     * Start the cron service to match orders every minute
     */
    public startOrderMatchingService() {
        return false;
        console.log('🔄 Starting Order Matching Cron Service...');
        
        // Run every minute: '0 * * * * *'
        cron.schedule('0 * * * * *', async () => {
            if (this.isRunning) {
                console.log('⏭️ Order matching already in progress, skipping...');
                return;
            }
            
            this.isRunning = true;
            try {
              //  await this.matchAndExecuteOrders();
            } catch (error: any) {
                console.error('❌ Error in order matching cron:', error);
            } finally {
                this.isRunning = false;
            }
        });

        console.log('✅ Order Matching Cron Service started - running every minute');
    }

    /**
     * Main function to find and execute matching orders
     */
    private async matchAndExecuteOrders() {
        try {
            console.log('🔍 Starting order matching process...');
            
            // Get all active orders
            const activeOrders = await this.getActiveOrders();
            
            if (!activeOrders || activeOrders.length === 0) {
                console.log('📊 No active orders found');
                return;
            }

            console.log(`📊 Found ${activeOrders.length} active orders to check for matches`);

            // Group orders by trading pair
            const ordersByPair = this.groupOrdersByTradingPair(activeOrders);
            
            let totalMatches = 0;
            let totalExecuted = 0;

            // Process each trading pair
            for (const [pair, orders] of ordersByPair.entries()) {
                const { buyOrders, sellOrders } = this.separateBuyAndSellOrders(orders);
                
                console.log(`📈 Processing pair ${pair}: ${buyOrders.length} buy orders, ${sellOrders.length} sell orders`);
                
                if (buyOrders.length === 0 || sellOrders.length === 0) {
                    continue;
                }

                // Find and execute matches
                const matches = this.findMatchingOrders(buyOrders, sellOrders);
                console.log(`🎯 Found ${matches.length} potential matches for pair ${pair}`);

                for (const match of matches) {
                    try {
                        const executed = await this.executeMatch(match);
                        if (executed) {
                            totalExecuted++;
                            console.log(`✅ Executed match: ${match.buyOrder.order_id} <-> ${match.sellOrder.order_id}`);
                        }
                    } catch (error: any) {
                        console.error(`❌ Failed to execute match: ${error.message}`);
                    }
                    totalMatches++;
                }
            }

            console.log(`🏁 Order matching completed: ${totalMatches} matches found, ${totalExecuted} executed`);

        } catch (error: any) {
            console.error('❌ Error in matchAndExecuteOrders:', error);
        }
    }

    /**
     * Get all active orders from the database
     */
    private async getActiveOrders(): Promise<any[]> {
        try {
            const query = `
                SELECT * FROM stellar_orders 
                WHERE status IN ('active', 'partially_filled')
                ORDER BY created_at ASC
            `;
            
            return await this.stellarTrading.callRawQuery(query);
        } catch (error: any) {
            console.error('❌ Error getting active orders:', error);
            return [];
        }
    }

    /**
     * Group orders by trading pair (e.g., "USDT/UGX")
     */
    private groupOrdersByTradingPair(orders: any[]): Map<string, any[]> {
        const pairs = new Map<string, any[]>();

        for (const order of orders) {
            // Create a consistent pair identifier
            // For buy orders: buying_asset/selling_asset
            // For sell orders: selling_asset/buying_asset
            let pair: string;
            
            if (order.order_type === 'buy') {
                pair = `${order.buying_asset}/${order.selling_asset}`;
            } else {
                pair = `${order.selling_asset}/${order.buying_asset}`;
            }

            if (!pairs.has(pair)) {
                pairs.set(pair, []);
            }
            pairs.get(pair)!.push(order);
        }

        return pairs;
    }

    /**
     * Separate orders into buy and sell arrays
     */
    private separateBuyAndSellOrders(orders: any[]): { buyOrders: any[], sellOrders: any[] } {
        const buyOrders = orders.filter(order => order.order_type === 'buy');
        const sellOrders = orders.filter(order => order.order_type === 'sell');

        return { buyOrders, sellOrders };
    }

    /**
     * Find matching orders based on price compatibility
     */
    private findMatchingOrders(buyOrders: any[], sellOrders: any[]): Array<{buyOrder: any, sellOrder: any, amount: number}> {
        const matches: Array<{buyOrder: any, sellOrder: any, amount: number}> = [];

        // Sort buy orders by price (highest first - best buyers)
        const sortedBuyOrders = buyOrders.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
        
        // Sort sell orders by price (lowest first - best sellers)
        const sortedSellOrders = sellOrders.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));

        for (const buyOrder of sortedBuyOrders) {
            const buyPrice = parseFloat(buyOrder.price);
            const buyRemaining = parseFloat(buyOrder.amount) - parseFloat(buyOrder.filled_amount || '0');
            
            if (buyRemaining <= 0) continue;

            for (const sellOrder of sortedSellOrders) {
                const sellPrice = parseFloat(sellOrder.price);
                const sellRemaining = parseFloat(sellOrder.amount) - parseFloat(sellOrder.filled_amount || '0');
                
                if (sellRemaining <= 0) continue;

                // Skip if same client
                if (buyOrder.client_id === sellOrder.client_id) continue;

                // Check if prices are compatible (buy price >= sell price)
                if (buyPrice >= sellPrice) {
                    // Calculate trade amount (minimum of what each order can provide)
                    const tradeAmount = Math.min(buyRemaining, sellRemaining);
                    
                    // Use the sell price (more conservative)
                    const executionPrice = sellPrice;
                    
                    matches.push({
                        buyOrder,
                        sellOrder,
                        amount: tradeAmount
                    });

                    console.log(`🎯 Match found: Buy ${buyOrder.order_id} @ ${buyPrice} vs Sell ${sellOrder.order_id} @ ${sellPrice} -> Execute @ ${executionPrice} for ${tradeAmount}`);
                }
            }
        }

        return matches;
    }

    /**
     * Execute a matched trade between buy and sell orders
     */
    private async executeMatch(match: {buyOrder: any, sellOrder: any, amount: number}): Promise<boolean> {
        try {
            const { buyOrder, sellOrder, amount } = match;
            
            console.log(`🔄 Executing match: ${amount} units between buy ${buyOrder.order_id} and sell ${sellOrder.order_id}`);

            // Use the existing executeSingleTrade method from StellarTrading
            const result = await this.stellarTrading.executeSingleTrade(
                buyOrder,
                sellOrder,
                amount.toString(),
                (amount * parseFloat(sellOrder.price)).toString(),
                'CRON_MATCH'
            );

            if (result.success) {
                console.log(`✅ Trade executed successfully: ${result.stellarHash}`);
                return true;
            } else {
                console.error(`❌ Trade execution failed: ${result.message}`);
                return false;
            }

        } catch (error: any) {
            console.error(`❌ Error executing match:`, error);
            return false;
        }
    }

  

    /**
     * Manually trigger order matching (for testing)
     */
    public async triggerManualMatching(): Promise<void> {
        if (this.isRunning) {
            console.log('⏭️ Order matching already in progress, skipping manual trigger...');
            return;
        }
        
        this.isRunning = true;
        try {
            await this.matchAndExecuteOrders();
        } finally {
            this.isRunning = false;
        }
    }
}

export default OrderMatchingService;
