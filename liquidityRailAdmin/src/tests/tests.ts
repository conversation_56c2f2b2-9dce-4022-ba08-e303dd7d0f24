// test.ts

import kotaniPay from "../helpers/kotani";
import CNGNUtility from "../helpers/CNGNUtility";
import { Network } from "cngn-typescript-library";
import MudaPayment from "../helpers/MudaPayment";
import Accounts from "../models/accounts";
import Quidax from "../helpers/Quidax";
import { WebhookSender } from "../helpers/webhookSender";
import HoneyCoin from "../helpers/HoneyCoin";
import { MudaProvider } from "../helpers/MudaProvider";
import { runUniversalOfframpTests, runSingleUniversalTest } from "./universal-offramp.test";


export async function runTests() {
    // createPaymentIntent();

    MudaPayment.getJWT();
}

/**
 * Run Universal Off-ramp tests
 */
export async function runUniversalTests() {
    console.log('🌍 Starting Universal Off-ramp Test Suite...\n');
    await runUniversalOfframpTests();
}

/**
 * Run a specific Universal Off-ramp test
 */
export async function runSpecificUniversalTest(testName: string) {
    console.log(`🌍 Running specific Universal Off-ramp test: ${testName}\n`);
    await runSingleUniversalTest(testName);
}
