import * as cron from 'node-cron';
import Transactions from '../models/transactions';
import ThirdParty from '../models/thirdParty';
import PegaPay from '../intergrations/PegPay';
import ThirdPartyHandler from './ThirdPartyHandler';
import MyFX from '../intergrations/MyFX';
import Model, { Steps } from './model';
import { StatusCodes } from '../intergrations/interfaces';
import { get } from './httpRequest';
import { getItem, setItem } from './connectRedis';
import PollingService from '../services/polling.collections.service';
import PollingPayoutsService from '../services/polling.payouts.service';
import { SweeperService } from '../services/sweeper.service';
import InternalModel from '../models/internal';
import { adminAlertsService } from '../services/adminAlerts.service';
const pollingService = new PollingService();
const pollingPayoutsService = new PollingPayoutsService();
const internalModel = new InternalModel();
class CronService {
    private thirdparty: ThirdParty;
    private transactions_: Transactions;
    constructor() {
        console.log("Cron Service initiated.");
        this.scheduleEveryThirtySeconds();
        this.scheduleEveryFourtySeconds();
        this.scheduleEveryOneMinutes();
        this.scheduleEverySixHours();
        this.scheduleEveryMinute();
        this.scheduleEveryThirtyMinutes();
        this.thirdparty = new ThirdParty();
        this.transactions_ = new Transactions();
        
        // Run balance check immediately on startup
        console.log("Running initial balance check...");
        this.checkBalancesAndAlert();
    }

    private scheduleEverySixHours() {
        cron.schedule('0 */6 * * *', async () => {
            console.log('Running every six hours task...');
            try {
                MyFX.getJWT()
                this.sweeperTask()
                
                // Add your logic here
                console.log('Every six hours task completed.');
            } catch (error) {
                console.error('Error running every six hours task:', error);
            }
        });
    }

    private scheduleEveryMinute() {
        cron.schedule('* * * * *', async () => {
            console.log('Running every minute task...');
            try {
                const transactions = new Transactions();
                transactions.ReverseTransaction({
                    token: "mdxQaUg",
                    userId: "123"
                })

                // Add your logic here
                console.log('Every minute task completed.');
            } catch (error) {
                console.error('Error running every minute task:', error);
            }
        });
    }

    private scheduleEveryFourtySeconds() {
        cron.schedule('*/40 * * * * *', this.everyFourtySecondsTask);
    }
 

    private scheduleEveryThirtySeconds() {
        cron.schedule('*/30 * * * * *', this.everyThirtySecondsTask);
    }

    private scheduleEveryThirtyMinutes() {
        cron.schedule('*/30 * * * *', this.everyThirtyMinutesTask);
    }

    private scheduleEveryOneMinutes() {
        cron.schedule('*/1 * * * *', this.everyOneMinutesTask);
    }

 
    private everyThirtySecondsTask = async () => {
        pollingService.getCollectionTransactions();
        console.log('Task running every 30 seconds, checking pending transactions...');
    };

    private everyThirtyMinutesTask = async () => {
        await this.checkBalancesAndAlert();
        console.log('Task running every 30 minutes, checking balances...');
    };

    private everyFourtySecondsTask() {
        pollingPayoutsService.checkPendingDirectPayouts();
        console.log('Task running every 40 seconds, checking pending transactions...');
    }

    private everyOneMinutesTask = async () => {
    //    this.checkPendingHoneyCoinCollections();
        console.log('Task running every 5 minutess, checking pending transactions...');
    };

    private sweeperTask = async () => {
        try {
            const sweeper = new SweeperService();
            const result = await sweeper.sweepFromAllWallets({
                dryRun: false,
                memo: 'Automated sweep to cold storage',
            });
        } catch (error: any) {
            console.error('Test failed:', error.message);
        }
    }

    private checkBalancesAndAlert = async () => {
        try {
            // Get all balances from the internal model
            const response = await internalModel.balanceManagement();
            
            if (response.statusCode !== 200) {
                throw new Error('Failed to fetch balances: ' + response.message);
            }

            const balances = response.data;
            
            // Format balances for alert service
            const formattedBalances = this.formatBalancesForAlert(balances);
            
            // Check thresholds and send alerts if needed
            const lowBalances = await adminAlertsService.checkBalanceThresholds(formattedBalances);
            
            if (lowBalances.length > 0) {
                console.log(`⚠️  Found ${lowBalances.length} accounts with low balances`);
            }

        } catch (error: any) {
            console.error('Error checking balances:', error);
            // Send error alert to admins
            await adminAlertsService.sendErrorAlert(error, 'balance-monitor-cron');
        }
    }

    private formatBalancesForAlert(balances: any): any[] {
        const formatted: any[] = [];

        try {
            // Process Utila balances
            if (balances.utilaBalances) {
                // Gas wallet balances
                if (balances.utilaBalances.gasBalances && Array.isArray(balances.utilaBalances.gasBalances)) {
                    balances.utilaBalances.gasBalances.forEach((bal: any) => {
                        formatted.push({
                            account: `Utila Gas Wallet (${balances.walletIds?.gas || 'N/A'})`,
                            currency: bal.asset_code || bal.currency,
                            balance: parseFloat(bal.balance || '0')
                        });
                    });
                }

                // Cold wallet balances
                if (balances.utilaBalances.coldBalances && Array.isArray(balances.utilaBalances.coldBalances)) {
                    balances.utilaBalances.coldBalances.forEach((bal: any) => {
                        formatted.push({
                            account: `Utila Cold Wallet (${balances.walletIds?.cold || 'N/A'})`,
                            currency: bal.asset_code || bal.currency,
                            balance: parseFloat(bal.balance || '0')
                        });
                    });
                }

                // Payout wallet balances
                if (balances.utilaBalances.payoutBalances && Array.isArray(balances.utilaBalances.payoutBalances)) {
                    balances.utilaBalances.payoutBalances.forEach((bal: any) => {
                        formatted.push({
                            account: `Utila Payout Wallet (${balances.walletIds?.payout || 'N/A'})`,
                            currency: bal.asset_code || bal.currency,
                            balance: parseFloat(bal.balance || '0')
                        });
                    });
                }

                // Main Utila balances
                if (balances.utilaBalances.utilaBalances && Array.isArray(balances.utilaBalances.utilaBalances)) {
                    balances.utilaBalances.utilaBalances.forEach((bal: any) => {
                        formatted.push({
                            account: 'Utila Main Wallet',
                            currency: bal.asset_code || bal.currency,
                            balance: parseFloat(bal.balance || '0')
                        });
                    });
                }
            }

            // Process PegPay balances
            if (balances.pegPay) {
                if (balances.pegPay.pull) {
                    formatted.push({
                        account: 'PegPay Pull',
                        currency: 'UGX',
                        balance: parseFloat(balances.pegPay.pull || '0')
                    });
                }

                if (balances.pegPay.push) {
                    formatted.push({
                        account: 'PegPay Push',
                        currency: 'UGX',
                        balance: parseFloat(balances.pegPay.push || '0')
                    });
                }
            }

        } catch (error: any) {
            console.error('Error formatting balances:', error);
        }

        return formatted;
    }

    // Public method to manually trigger balance check
    public async manualBalanceCheck() {
        console.log('Manual balance check triggered...');
        await this.checkBalancesAndAlert();
    }
}

export default CronService;