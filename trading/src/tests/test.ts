import StellarService from "../helpers/StellarService";
import StellarSDEXTrading from "../models/StellarSDEXTrading";

async function test() {

    const resp1 = await new StellarSDEXTrading().getOrderBook('USDT', 'UGX', 'TEST');
    console.log('🧪 Testing getOrderBook with:', resp1);
    return;
    
    console.log('🧪 Testing createLimitOffer with:');
    console.log('🧪 Testing createLimitOffer with:');
    console.log('🧪 Testing createLimitOffer with:');
    const result = await new StellarService().createLimitOffer({
        senderSecretKey: 'SBIEU7X4QPGT3AUQNNC6S5NJBZHZQBRA7E6HXQMZ4JDXBRXDEW2VXQV4',
        baseAsset: 'USDT',
        counterAsset: 'KES',
        amount: '2',
        price: '210.000000',
        orderType: 'buy'
    });
    console.log('🧪 Testing createLimitOffer with:', result);
}
test();
