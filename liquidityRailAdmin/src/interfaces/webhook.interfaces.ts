// Base webhook interface

export interface Quote {
    transId: string;
    provider_id: string;
    company_id: number;
    send_asset: string;
    send_amount: string;
    receive_currency: string;
    receive_amount: number;
    chain: string;
    payable_amount: number;
    ex_rate: string;
    account_number: string;
    service_id: string;
    receiver_address: string;
    pay_in_status: string; // example: 'PENDING'
    status: string;
    sending_address: string;
    response_body?: string | null;
    reason?: string | null;
    created_on: string; // ISO timestamp format
    bank_name?: string | null;
    bank_code?: string | null;
    provider_ref_id?: string | null;
    client_reference_id?: string | null;
    expires_at?: string | null;
    provider_address?: string | null;
    provider_memo?: string | null;
    fee?: number | null;
    fee_currency?: string | null;
    payment_method_id?: string | null;
    narration?: string | null;
    hash?: string | null;
    contract_address?: string | null;
    direction?: string | null;
}


interface BaseWebhook {
    provider: string;
    transactionId: string;
    status: string;
    eventType: string;
}

export interface HoneyCoinWebhook {
    event:string,
    data: {
      transactionId: string,
      status: string,
      type: string,
      externalReference: string,
      method: string,
      depositAddress: string,
      txId: string
    },
    timestamp: string,
    ip: string
  }

// Crypto received webhooks (Moralis, Fireblocks incoming transactions)
export interface CryptoReceivedWebhook extends BaseWebhook {
    eventType: 'crypto_received';
    provider: 'moralis' | 'fireblocks' | 'quidax' | 'stellar' | 'muda';
    amount: string;
    hash: string;
    from_address: string;
    to_address: string;
    asset_code: string;
    contract_address?: string;
    fee?: string;
    currency?: string;
    memo?: string;
}

// Crypto sent webhooks (Fireblocks, Quidax outgoing transactions)
export interface CryptoSentWebhook extends BaseWebhook {
    eventType: 'crypto_sent';
    provider: 'fireblocks' | 'quidax';
    amount: string;
    hash: string;
    to_address: string;
    asset_code: string;
    fee?: string;
}

// Fiat received webhooks (Exchange deposits, bank transfers)
export interface FiatReceivedWebhook extends BaseWebhook {
    eventType: 'fiat_received';
    provider: 'quidax' | 'kotani';
    amount: string;
    currency: string;
    deposit_address?: string;
    account_number?: string;
}

// Fiat sent webhooks (MudaPayment, Ogateway, successful payouts)
export interface FiatSentWebhook extends BaseWebhook {
    eventType: 'fiat_sent';
    provider: 'muda' | 'ogateway' | 'kotani';
    amount: string;
    currency: string;
    account_number: string;
    reference_id?: string;
}

// General status webhooks (for any provider status updates)
export interface GeneralStatusWebhook extends BaseWebhook {
    eventType: 'status_update';
    provider: 'general';
    reference_id?: string;
    hash?: string;
    amount?: string;
    currency?: string;
}

// Union type for all webhook types
export type WebhookPayload =
    | CryptoReceivedWebhook
    | CryptoSentWebhook
    | FiatReceivedWebhook
    | FiatSentWebhook
    | GeneralStatusWebhook;

// Legacy CryptoWebhookData interface (for Moralis parsing)
export interface CryptoWebhookData {
    amount: string;
    asset_code: string;
    contract_address: string;
    fee: string;
    from_address: string;
    to_address: string;
    date: string;
    confirmation: boolean;
}

// Payment method interfaces
export interface BankPayment {
    kotani_customer_key: string,
    company_id: string;
    type: 'bank';
    bank_name: string;
    bank_code: string;
    currency: string;
    account_number: string;
    bank_address?: string;
    bank_phone_number?: string;
    phone_number?: string;
    bank_country?: string;
    account_name?: string;
    beneficiary_address?: string;
    beneficiary_phone_number?: string;
    provider_id?: string;
    provider_customer_key?: string;
    bank_branch_code?: string;

    sort_code?: string;
    swift_code?: string;
}

export interface MobileMoneyPayment {
    kotani_customer_key: string,
    company_id: string;
    type: 'mobile_money';
    currency: string;
    phone_number: string;
    country_code: string;
    network?: string;
    account_name?: string;
}

// Payout interfaces
export interface PayoutRequest {
    transId: string;
    currency: string;
    fiatAmount: string;
    amount: string;
    providerId: string;
    serviceId: string;
    asset_code: string;
    accountNumber: string;
    accountName?: string;
    provider?: string;
    paymentMethodId?: string;
    chain?: string;
}

export interface PayoutResponse {
    success: boolean;
    status: string;
    message: string;
    transactionId?: string;
    providerResponse?: any;
}

// Helper type for webhook creation
export interface WebhookFactory {
    createCryptoReceived(data: any, provider: 'moralis' | 'fireblocks'): CryptoReceivedWebhook;
    createFiatSent(data: any, provider: 'muda' | 'ogateway' | 'kotani'): FiatSentWebhook;
    createFiatReceived(data: any, provider: 'quidax' | 'kotani'): FiatReceivedWebhook;
    createGeneralStatus(data: any): GeneralStatusWebhook;
}

export interface StellarPayment {
    transId: string;
    currency: string;
    fiatAmount: string;
    amount: string;
    providerId: string;
    serviceId: string;
    asset_code: string;
    accountNumber: string;
    accountName?: string;
    provider?: string;
    paymentMethodId?: string;
    chain?: string;
}
export interface LRProviderEvents {
    eventType: 'fiat_sent' | 'fiat_received' | 'crypto_received' | 'crypto_sent';
    transaction_id: string;
    reference_id: string;
    status: 'SUCCESS' | 'PENDING' | 'FAILED';
    data: fiatEvent | cryptoEvent;
}
interface fiatEvent {
    currency: string;
    amount: number;
    amount_delivered: number;
    fee: number;
    external_reference_id: string;
    payment_type: string;
    payment_method_id: string;
}
interface cryptoEvent {
    amount: string;
    chain: string;
    asset_code: string;
    hash: string;
    from_address: string;
    to_address: string;
    contract_address: string;
    fee: string;
}

export interface MudaWebhook {
    type: string;
    event: string;
    statusCode: number;
    message: string;
    timestamp: string;
    trans_type: string;
    reference_id: string;
    status: string;
    amount: string;
    client_id: string;
    currency: string;
    sender_account: string;
    receiver_account: string;
    transaction_id: string;
    fee: string;
    meta: string;
    chainInfo?: {
        from_address: string;
        to_address: string;
        amount: string;
        asset_code: string;
        contract_address: string;
        hash: string;
        state: string;
        direction: string;
    }
}