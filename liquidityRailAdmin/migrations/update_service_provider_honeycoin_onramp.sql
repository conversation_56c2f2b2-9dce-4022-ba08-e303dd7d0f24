-- Update or insert service_provider record for service_id=1011 and provider_id=6

-- Update if record exists
UPDATE service_providers 
SET min_amount = 50,
    max_amount = 500000,
    fee = 0.00,
    block_fee_usdt = 0,
    fee_type = 'percentage'
WHERE service_id = 1011 AND provider_id = 6;

-- Insert if record doesn't exist
INSERT INTO service_providers (service_id, provider_id, min_amount, max_amount, fee, block_fee_usdt, fee_type)
SELECT 1011, 6, 50, 500000, 0.00, 0, 'percentage'
WHERE NOT EXISTS (
    SELECT 1 FROM service_providers 
    WHERE service_id = 1011 AND provider_id = 6
);

