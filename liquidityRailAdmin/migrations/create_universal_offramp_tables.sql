-- Universal Off-ramp Database Schema
-- These tables track multi-step transaction routes and execution

-- Table to store route definitions and overall transaction state
CREATE TABLE IF NOT EXISTS `universal_routes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quote_id` varchar(50) NOT NULL COMMENT 'Reference to quotes.transId',
  `route_type` enum('direct', 'multi_step') NOT NULL DEFAULT 'direct',
  `total_steps` int(11) NOT NULL DEFAULT 1,
  `current_step` int(11) NOT NULL DEFAULT 0 COMMENT 'Currently executing step (0 = not started)',
  `completed_steps` int(11) NOT NULL DEFAULT 0,
  `failed_steps` int(11) NOT NULL DEFAULT 0,
  `overall_status` enum('pending', 'executing', 'completed', 'failed', 'cancelled', 'expired') NOT NULL DEFAULT 'pending',
  `total_fees` decimal(18,8) NOT NULL DEFAULT 0.00000000,
  `final_amount` decimal(18,8) NOT NULL DEFAULT 0.00000000,
  `estimated_duration_minutes` int(11) NULL COMMENT 'Estimated total duration',
  `execution_started_at` timestamp NULL,
  `execution_completed_at` timestamp NULL,
  `failure_reason` text NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `quote_id_unique` (`quote_id`),
  KEY `idx_quote_id` (`quote_id`),
  KEY `idx_overall_status` (`overall_status`),
  KEY `idx_route_type` (`route_type`),
  KEY `idx_current_step` (`current_step`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Universal off-ramp route definitions and execution state';

-- Table to store individual route steps and their execution details
CREATE TABLE IF NOT EXISTS `route_steps` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quote_id` varchar(50) NOT NULL COMMENT 'Reference to quotes.transId',
  `step_index` int(11) NOT NULL COMMENT '1-based step number in the route',
  `step_type` enum('swap', 'bridge', 'payout') NOT NULL,
  `provider` varchar(50) NOT NULL COMMENT 'Provider name (Rhino, Quidax, HoneyCoin, etc.)',
  `provider_id` int(11) NULL COMMENT 'Provider ID from providers table',
  `from_token` varchar(50) NOT NULL,
  `to_token` varchar(50) NOT NULL,
  `from_chain` varchar(50) NOT NULL,
  `to_chain` varchar(50) NOT NULL,
  `estimated_amount` decimal(18,8) NOT NULL COMMENT 'Expected output amount',
  `actual_amount` decimal(18,8) NULL COMMENT 'Actual received amount',
  `estimated_fee` decimal(18,8) NOT NULL COMMENT 'Expected fee',
  `actual_fee` decimal(18,8) NULL COMMENT 'Actual fee charged',
  `status` enum('pending', 'executing', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
  `provider_quote_id` varchar(100) NULL COMMENT 'Provider-specific quote/transaction ID',
  `provider_transaction_id` varchar(100) NULL COMMENT 'Provider transaction reference',
  `transaction_hash` varchar(100) NULL COMMENT 'Blockchain transaction hash (for crypto steps)',
  `smart_deposit_address` varchar(255) NULL COMMENT 'Rhino smart deposit address (for swap/bridge steps)',
  `destination_address` varchar(255) NULL COMMENT 'Destination address for this step',
  `error_message` text NULL,
  `provider_response` json NULL COMMENT 'Full provider response data',
  `execution_started_at` timestamp NULL,
  `execution_completed_at` timestamp NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `quote_step_unique` (`quote_id`, `step_index`),
  KEY `idx_quote_id` (`quote_id`),
  KEY `idx_step_index` (`step_index`),
  KEY `idx_status` (`status`),
  KEY `idx_provider` (`provider`),
  KEY `idx_provider_quote_id` (`provider_quote_id`),
  KEY `idx_provider_transaction_id` (`provider_transaction_id`),
  FOREIGN KEY (`quote_id`) REFERENCES `quotes` (`transId`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Individual steps in universal off-ramp routes';

-- Table to track step execution logs for debugging and monitoring
CREATE TABLE IF NOT EXISTS `route_step_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quote_id` varchar(50) NOT NULL,
  `step_index` int(11) NOT NULL,
  `log_type` enum('info', 'warning', 'error', 'debug') NOT NULL DEFAULT 'info',
  `message` text NOT NULL,
  `data` json NULL COMMENT 'Additional log data',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_quote_id` (`quote_id`),
  KEY `idx_step_index` (`step_index`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Execution logs for route steps';

-- Add route_type column to existing quotes table to distinguish universal quotes
ALTER TABLE `quotes` 
ADD COLUMN `route_type` enum('direct', 'multi_step') NULL DEFAULT 'direct' COMMENT 'Type of route: direct provider or multi-step universal' 
AFTER `transaction_type`;

-- Add index for route_type
ALTER TABLE `quotes` 
ADD INDEX `idx_route_type` (`route_type`);

-- View for easy monitoring of universal off-ramp transactions
CREATE OR REPLACE VIEW `universal_offramp_status` AS
SELECT 
    q.transId as quote_id,
    q.client_reference_id,
    q.status as quote_status,
    q.pay_in_status,
    q.send_amount,
    q.send_asset,
    q.receive_currency,
    q.receive_amount,
    q.created_on,
    ur.route_type,
    ur.total_steps,
    ur.current_step,
    ur.completed_steps,
    ur.overall_status as route_status,
    ur.total_fees as route_fees,
    ur.final_amount as route_final_amount,
    ur.execution_started_at,
    ur.execution_completed_at,
    CASE 
        WHEN ur.total_steps > 0 THEN ROUND((ur.completed_steps / ur.total_steps) * 100, 2)
        ELSE 0 
    END as completion_percentage
FROM quotes q
LEFT JOIN universal_routes ur ON q.transId = ur.quote_id
WHERE q.route_type = 'multi_step' OR ur.quote_id IS NOT NULL
ORDER BY q.created_on DESC;

-- View for current step details
CREATE OR REPLACE VIEW `current_step_details` AS
SELECT 
    rs.quote_id,
    rs.step_index,
    rs.step_type,
    rs.provider,
    rs.from_token,
    rs.to_token,
    rs.from_chain,
    rs.to_chain,
    rs.status as step_status,
    rs.estimated_amount,
    rs.actual_amount,
    rs.estimated_fee,
    rs.actual_fee,
    rs.provider_quote_id,
    rs.provider_transaction_id,
    rs.transaction_hash,
    rs.smart_deposit_address,
    rs.error_message,
    rs.execution_started_at,
    rs.execution_completed_at,
    ur.current_step,
    ur.overall_status
FROM route_steps rs
INNER JOIN universal_routes ur ON rs.quote_id = ur.quote_id
WHERE rs.step_index = ur.current_step OR (ur.current_step = 0 AND rs.step_index = 1)
ORDER BY rs.quote_id, rs.step_index;
