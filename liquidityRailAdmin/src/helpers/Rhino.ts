import RequestHelper from "./request.helper";
import axios, { AxiosResponse } from 'axios';
import dotenv from 'dotenv';
import { mapToRhinoChain } from './rhinoTypes';
import {
  RhinoAuthRequest,
  RhinoCommitRequest,
  RhinoCommitResponse,
  RhinoStatusResponse,
  RhinoBridgeConfigResponse,
  RhinoSwapConfigResponse,
  UnifiedBridgeSwapRequest,
  UnifiedBridgeSwapResponse,
  RhinoBridgeQuoteRequest,
  RhinoBridgeQuoteResponse,
  RhinoSwapQuoteRequest,
  RhinoSwapQuoteResponse
} from './rhinoTypes';

dotenv.config();

// Standard API Response Structure following existing pattern
interface RhinoApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  status?: number;
  // Additional error properties for specific error types
  issues?: any[];
  unsupportedChains?: string[];
  addresses?: string[];
}

class Rhino {
  private apiUrl: string;
  private apiKey: string;
  private requestHeaders: Record<string, string>;

  constructor() {
    // Validate required environment variables
    const requiredEnvVars = [
      { key: 'RHINO_API_KEY', description: 'Rhino API authentication key' },
      { key: 'RHINO_API_URL', description: 'Rhino API base URL' }
    ];

    this.validateRequiredEnvVars(requiredEnvVars);

    this.apiKey = process.env.RHINO_API_KEY!;
    this.apiUrl = process.env.RHINO_API_URL!;

    this.requestHeaders = {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    };

    console.log('Rhino integration initialized', {
      apiUrl: this.apiUrl,
      hasApiKey: !!this.apiKey
    });
  }

  private validateRequiredEnvVars(requiredVars: Array<{ key: string; description: string }>): void {
    const missingVars: string[] = [];

    for (const envVar of requiredVars) {
      if (!process.env[envVar.key]) {
        missingVars.push(`${envVar.key} (${envVar.description})`);
      }
    }

    if (missingVars.length > 0) {
      console.log("Rhino missing environment variables:", missingVars);
    }
  }

  /**
   * Authenticate with Rhino API to get JWT token (no caching)
   */
  private async authenticate(): Promise<string | null> {
    const requestId = `RHINO_AUTH_${Date.now()}`;

    try {
      console.log(`${requestId}_REQUEST`, 'Rhino: Authenticating with API');

      const authRequest: RhinoAuthRequest = {
        apiKey: this.apiKey
      };

      const response: AxiosResponse = await axios.post(
        `${this.apiUrl}/authentication/auth/apiKey`,
        authRequest,
        {
          headers: this.requestHeaders
        }
      );

      console.log(`${requestId}_RESPONSE`, response.data);

      // Rhino.fi returns 'jwt' field, not 'token'
      if (response.data && response.data.jwt) {
        console.log(`${requestId}_SUCCESS`, 'Rhino: Authentication successful', {
          userId: response.data.userId
        });
        return response.data.jwt;
      }

      console.error(`${requestId}_ERROR`, 'Failed to generate JWT token - no jwt in response');
      return null;
    } catch (error: any) {
      console.error(`${requestId}_ERROR`, {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
      return null;
    }
  }

  /**
   * Make authenticated request to Rhino.fi API following existing patterns
   */
  private async makeRequest(
    method: 'get' | 'post' | 'put' | 'patch',
    endpoint: string,
    data?: any,
    requiresAuth: boolean = true
  ): Promise<any> {
    const requestId = `RHINO_${method.toUpperCase()}_${Date.now()}`;

    try {
      const fullUrl = `${this.apiUrl}${endpoint}`;

      console.log(`${requestId}_REQUEST`, {
        url: fullUrl,
        payload: data,
        requiresAuth
      });

      // Set up headers
      let headers = { ...this.requestHeaders };

      if (requiresAuth) {
        const token = await this.authenticate();
        if (!token) {
          return {
            success: false,
            status: 401,
            message: 'Authentication failed',
            error: 'Failed to obtain authentication token'
          };
        }
        // Rhino.fi expects just the JWT token without "Bearer " prefix
        headers['Authorization'] = token;
      }

      // Use RequestHelper following existing pattern
      console.log(`${requestId}_SETUP`, {
        endpoint: fullUrl,
        method: method,
        headers: headers,
        data: data
      });

      RequestHelper.setEndpoint(fullUrl);
      RequestHelper.setHeaders(headers);

      if (data && Object.keys(data).length > 0) {
        RequestHelper.setData(data);
        console.log(`${requestId}_DATA_SET`, { data });
      }

      console.log(`${requestId}_MAKING_REQUEST`, { method: `${method}Request` });
      const response = await RequestHelper[`${method}Request`]();
      console.log(`${requestId}_RESPONSE_RAW`, { 
        response, 
        responseType: typeof response,
        responseKeys: response ? Object.keys(response) : 'null/undefined'
      });
      
      const errors = await RequestHelper.getErrors();
      console.log(`${requestId}_ERRORS_RAW`, { errors });
      
      // If we have an error, let's also check what the actual axios error was
      if (errors && Object.keys(errors).length > 0) {
        console.log(`${requestId}_HAS_ERRORS`, { errors });
      }

      if (errors?.status === "error") {
        console.error(`${requestId}_ERROR`, {
          endpoint,
          error: errors.message,
          errorData: errors
        });

        // Handle different Rhino error response formats
        const errorData = errors.data || {};

        // Check for specific Rhino error formats
        if (errorData._tag === 'HttpApiDecodeError' || errorData.issues) {
          return {
            success: false,
            status: 400,
            message: errorData.message || 'API decode error',
            error: errorData.message || 'API decode error',
            _tag: errorData._tag,
            issues: errorData.issues
          };
        }

        if (errorData._tag === 'Unauthorized') {
          return {
            success: false,
            status: 401,
            message: errorData.message || 'Unauthorized',
            error: errorData.message || 'Unauthorized',
            _tag: errorData._tag
          };
        }

        if (errorData._tag === 'WithdrawLimitReached') {
          return {
            success: false,
            status: 422,
            message: 'Withdraw limit reached',
            error: 'Withdraw limit reached',
            _tag: errorData._tag,
            token: errorData.token,
            chain: errorData.chain,
            receiveAmount: errorData.receiveAmount,
            maxWithdrawAmount: errorData.maxWithdrawAmount
          };
        }

        if (errorData._tag === 'NoRouteFoundError') {
          return {
            success: false,
            status: 404,
            message: 'No route found for this token pair',
            error: 'No route found',
            _tag: errorData._tag,
            tokenIn: errorData.tokenIn,
            tokenOut: errorData.tokenOut,
            chainIn: errorData.chainIn,
            chainOut: errorData.chainOut
          };
        }

        if (errorData._tag === 'EndpointDisabledError') {
          return {
            success: false,
            status: 503,
            message: errorData.message || 'Endpoint temporarily disabled',
            error: 'Service unavailable',
            _tag: errorData._tag
          };
        }

        if (errorData._tag === 'QuoteNotFound') {
          return {
            success: false,
            status: 404,
            message: errorData.message || 'Quote not found',
            error: 'Quote not found',
            _tag: errorData._tag
          };
        }

        if (errorData._tag === 'InvalidRequest') {
          // Default to 422, will be overridden in catch block if needed
          return {
            success: false,
            status: 422,
            message: errorData.message || 'Invalid request',
            error: 'Invalid request',
            _tag: errorData._tag
          };
        }

        return {
          success: false,
          status: 500,
          message: errors.message || 'Request failed',
          error: errors.message
        };
      }

      const results = await RequestHelper.getResults();
      console.log(`${requestId}_GET_RESULTS`, { results });
      console.log(`${requestId}_RESPONSE`, results);

      // Normalize response format to include success flag
      if (results && typeof results === 'object') {
        // If response has status: true, treat as success
        if (results.status === true) {
          return {
            success: true,
            data: results.data,
            status: results.status
          };
        }
        // If response has data but no explicit status, treat as success
        if (results.data && !results.hasOwnProperty('status')) {
          return {
            success: true,
            data: results.data
          };
        }
        // If response has depositAddress field (successful Rhino response), treat as success
        if (results.depositAddress || results.bridges) {
          return {
            success: true,
            data: results
          };
        }
        // If response has error indicators, treat as failure
        if (results._tag || results.error || results.status === false) {
          return {
            success: false,
            ...results
          };
        }
        // Default to success for valid responses
        return {
          success: true,
          ...results
        };
      }


      return results;
    } catch (error: any) {
      console.error(`${requestId}_ERROR`, {
        endpoint,
        error: error.message,
        responseData: error.response?.data
      });

      // Handle Rhino error responses in catch block too
      const errorData = error.response?.data || {};

      if (errorData._tag === 'HttpApiDecodeError' || errorData.issues) {
        return {
          success: false,
          status: 400,
          message: errorData.message || 'API decode error',
          error: errorData.message || 'API decode error',
          _tag: errorData._tag,
          issues: errorData.issues
        };
      }

      if (errorData._tag === 'Unauthorized') {
        return {
          success: false,
          status: 401,
          message: errorData.message || 'Unauthorized',
          error: errorData.message || 'Unauthorized',
          _tag: errorData._tag
        };
      }

      if (errorData._tag === 'WithdrawLimitReached') {
        return {
          success: false,
          status: 422,
          message: 'Withdraw limit reached',
          error: 'Withdraw limit reached',
          _tag: errorData._tag,
          token: errorData.token,
          chain: errorData.chain,
          receiveAmount: errorData.receiveAmount,
          maxWithdrawAmount: errorData.maxWithdrawAmount
        };
      }

      if (errorData._tag === 'NoRouteFoundError') {
        return {
          success: false,
          status: 404,
          message: 'No route found for this token pair',
          error: 'No route found',
          _tag: errorData._tag,
          tokenIn: errorData.tokenIn,
          tokenOut: errorData.tokenOut,
          chainIn: errorData.chainIn,
          chainOut: errorData.chainOut
        };
      }

      if (errorData._tag === 'EndpointDisabledError') {
        return {
          success: false,
          status: 503,
          message: errorData.message || 'Endpoint temporarily disabled',
          error: 'Service unavailable',
          _tag: errorData._tag
        };
      }

      if (errorData._tag === 'QuoteNotFound') {
        return {
          success: false,
          status: 404,
          message: errorData.message || 'Quote not found',
          error: 'Quote not found',
          _tag: errorData._tag
        };
      }

      if (errorData._tag === 'InvalidRequest') {
        // Check if it's a rate limit (429) or validation error (422)
        const status = error.response?.status === 429 ? 429 : 422;
        const message = status === 429 ?
          (errorData.message || 'Rate limit exceeded') :
          (errorData.message || 'Invalid request');

        return {
          success: false,
          status: status,
          message: message,
          error: status === 429 ? 'Rate limit exceeded' : 'Invalid request',
          _tag: errorData._tag
        };
      }

      return {
        success: false,
        status: error.response?.status || 500,
        message: error.response?.data?.message || error.message,
        error: error.message,
        data: error.response?.data
      };
    }
  }

  /**
   * Make public request (no auth required)
   */
  private async publicRequest<T>(endpoint: string): Promise<T | any> {
    const requestId = `RHINO_PUBLIC_${Date.now()}`;

    try {
      console.log(`${requestId}_REQUEST`, { endpoint });

      const response = await axios.get(`${this.apiUrl}${endpoint}`, {
        headers: this.requestHeaders
      });

      console.log(`${requestId}_RESPONSE`, response.data);
      return response.data;
    } catch (error: any) {
      console.error(`${requestId}_ERROR`, {
        endpoint,
        error: error.message,
        status: error.response?.status,
        responseData: error.response?.data
      });

      // For 400 errors, Rhino returns the error structure directly
      if (error.response?.status === 400 && error.response?.data) {
        return error.response.data;
      }

      return {
        success: false,
        status: error.response?.status || 500,
        message: error.response?.data?.message || error.message,
        error: error.message,
        data: error.response?.data
      };
    }
  }

  /**
   * Get deposit address status using Rhino.fi API
   * Endpoint: GET /bridge/deposit-addresses/{depositAddress}/{depositChain}
   */
  public async getDepositAddressStatus(
    depositAddress: string,
    depositChain: string
  ): Promise<RhinoApiResponse<any>> {
    try {
      console.log('Rhino: Getting deposit address status', {
        depositAddress: depositAddress.substring(0, 8) + '...',
        depositChain
      });

      const endpoint = `/bridge/deposit-addresses/${depositAddress}/${depositChain}`;
      const response = await this.makeRequest('get', endpoint);

      if (!response.success) {
        console.error('Rhino: Deposit address status check failed', response);

        // Handle specific error types based on Rhino API documentation
        if (response.status === 400 && response._tag === 'HttpApiDecodeError') {
          return {
            success: false,
            status: 400,
            error: 'Invalid request format',
            message: response.message || 'Request validation failed',
            issues: response.issues
          };
        }

        if (response.status === 401 && response._tag === 'Unauthorized') {
          return {
            success: false,
            status: 401,
            error: 'Unauthorized',
            message: response.message || 'Invalid API key'
          };
        }

        if (response.status === 422 && response._tag === 'DepositAddressNotFound') {
          return {
            success: false,
            status: 422,
            error: 'Deposit address not found',
            message: response.message || 'The specified deposit address was not found',
            addresses: response.addresses
          };
        }

        if (response.status === 503 && response._tag === 'EndpointDisabledError') {
          return {
            success: false,
            status: 503,
            error: 'Service unavailable',
            message: response.message || 'Deposit address status endpoint is currently disabled'
          };
        }

        return {
          success: false,
          status: response.status || 500,
          error: response.error || 'Status check failed',
          message: response.message || 'Failed to get deposit address status'
        };
      }

      console.log('Rhino: Deposit address status retrieved successfully', {
        isActive: response.data?.isActive,
        supportedTokens: response.data?.supportedTokens?.length || 0
      });

      return {
        success: true,
        data: response.data
      };

    } catch (error: any) {
      console.error('Rhino: Deposit address status check error:', error);
      return {
        success: false,
        status: 500,
        error: error.message,
        message: 'Failed to get deposit address status'
      };
    }
  }

  /**
   * Get bridge configuration (supported chains and tokens)
   */
  public async getBridgeConfigs(): Promise<RhinoApiResponse<RhinoBridgeConfigResponse>> {
    console.log('Rhino: Getting bridge configurations');

    const response = await this.publicRequest<RhinoBridgeConfigResponse>('/bridge/configs');

    // Check if response has error structure (400 error format)
    if (response._tag === 'HttpApiDecodeError' || response.issues) {
      console.error('Rhino: Failed to get bridge configurations', response);
      return {
        success: false,
        status: 400,
        error: response.message || 'API decode error',
        message: response.message || 'Failed to get bridge configurations'
      };
    }

    // Check for other error formats
    if (response.success === false) {
      console.error('Rhino: Failed to get bridge configurations', response);
      return {
        success: false,
        status: response.status,
        error: response.error,
        message: response.message
      };
    }

    console.log('Rhino: Bridge configurations retrieved successfully');
    return {
      success: true,
      data: response
    };
  }

  /**
   * Get swap configurations (supported chains and tokens)
   */
  public async getSwapConfigs(): Promise<RhinoApiResponse<RhinoSwapConfigResponse>> {
    console.log('Rhino: Getting swap configurations');

    // Use the correct endpoint for bridge-swap token configs
    const response = await this.publicRequest<RhinoSwapConfigResponse>('/bridge/bridge-swap-token-configs');

    // Check if response has error structure (400 error format)
    if (response._tag === 'HttpApiDecodeError' || response.issues) {
      console.error('Rhino: Failed to get swap configurations', response);
      return {
        success: false,
        status: 400,
        error: response.message || 'API decode error',
        message: response.message || 'Failed to get swap configurations'
      };
    }

    // Check for other error formats
    if (response.success === false) {
      console.error('Rhino: Failed to get swap configurations', response);
      return {
        success: false,
        status: response.status,
        error: response.error,
        message: response.message
      };
    }

    console.log('Rhino: Swap configurations retrieved successfully');
    return {
      success: true,
      data: response
    };
  }

  /**
   * Determine operation type from request
   */
private determineOperationType(
  request: UnifiedBridgeSwapRequest
): 'bridge' | 'swap' {
  console.log('Rhino: Determining operation type', {
    operationType: request.operationType,
    fromToken: request.fromToken,
    toToken: request.toToken,
    fromChain: request.fromChain,
    toChain: request.toChain,
    token: request.token
  });

  // 1. Respect explicit operationType if provided
  if (request.operationType === 'bridge' || request.operationType === 'swap') {
    return request.operationType;
  }

  // 2. Both fromToken and toToken specified
  if (request.fromToken && request.toToken) {
    // Different tokens (regardless of chains) => swap
    if (request.fromToken !== request.toToken) {
      return 'swap';
    }

    // Same token:
    if (request.fromChain && request.toChain && request.fromChain !== request.toChain) {
      return 'bridge'; // same token cross-chain
    }

    // Same token same chain => no swap/bridge (throw or default)
    throw new Error(
      'Cannot determine operation: same token and same chain specified.'
    );
  }

  // 3. Only a single token specified (your convention for bridge)
  if (request.token) {
    return 'bridge';
  }

  // 4. Fallback: cross-chain with different tokens => swap
  if (
    request.fromChain &&
    request.toChain &&
    request.fromChain !== request.toChain &&
    request.fromToken !== request.toToken
  ) {
    return 'swap';
  }

  // 5. Final default
  return 'bridge';
}


  /**
   * Get quote for bridge operation
   */
  private async getBridgeQuote(request: UnifiedBridgeSwapRequest): Promise<RhinoBridgeQuoteResponse | any> {
    console.log('Rhino: Getting bridge quote', {
      fromChain: request.fromChain,
      toChain: request.toChain,
      token: request.token || request.fromToken,
      amount: request.amount
    });

    // Use the correct Rhino API format with proper chain mapping
    const bridgeRequest = {
      token: request.token || request.fromToken!,
      chainIn: mapToRhinoChain(request.fromChain),
      chainOut: mapToRhinoChain(request.toChain),
      amount: request.amount,
      mode: request.mode || 'pay',
      depositor: request.depositor || request.recipient, // Use depositor if provided, fallback to recipient
      recipient: request.recipient
    };

    return this.makeRequest('post', '/bridge/quote/user', bridgeRequest);
  }

  /**
   * Get quote for bridge-swap operation
   */
  private async getBridgeSwapQuote(request: UnifiedBridgeSwapRequest): Promise<any> {
    console.log('Rhino: Getting bridge-swap quote', {
      fromChain: request.fromChain,
      toChain: request.toChain,
      tokenIn: request.fromToken,
      tokenOut: request.toToken,
      amount: request.amount
    });

    // Use the correct Rhino API format for bridge-swap with proper chain mapping
    const bridgeSwapRequest = {
      chainIn: mapToRhinoChain(request.fromChain),
      chainOut: mapToRhinoChain(request.toChain),
      tokenIn: request.fromToken!,
      tokenOut: request.toToken!,
      amount: request.amount,
      mode: 'pay', // Only pay mode supported for bridge-swaps
      depositor: request.depositor || request.recipient, // Use depositor if provided, fallback to recipient
      recipient: request.recipient
    };

    return this.makeRequest('post', '/bridge/quote/bridge-swap/user', bridgeSwapRequest);
  }

  /**
   * Get unified quote (automatically determines bridge vs swap)
   */
  public async getQuote(request: UnifiedBridgeSwapRequest): Promise<RhinoApiResponse<UnifiedBridgeSwapResponse>> {
    console.log('Rhino: Getting unified quote', request);

    // Validate required fields
    if (!request.fromChain || !request.toChain || !request.amount || !request.recipient) {
      console.error('Rhino: Missing required fields for quote');
      return {
        success: false,
        status: 400,
        error: 'Missing required fields: fromChain, toChain, amount, recipient',
        message: 'Missing required fields: fromChain, toChain, amount, recipient'
      };
    }

    const operationType = this.determineOperationType(request);

    let quoteResponse: any;

    if (operationType === 'swap') {
      if (!request.fromToken || !request.toToken) {
        console.error('Rhino: Missing tokens for swap operation');
        return {
          success: false,
          status: 400,
          error: 'fromToken and toToken are required for swap operations',
          message: 'fromToken and toToken are required for swap operations'
        };
      }

      quoteResponse = await this.getBridgeSwapQuote(request);
    } else {
      if (!request.token && !request.fromToken) {
        console.error('Rhino: Missing token for bridge operation');
        return {
          success: false,
          status: 400,
          error: 'token (or fromToken) is required for bridge operations',
          message: 'token (or fromToken) is required for bridge operations'
        };
      }

      quoteResponse = await this.getBridgeQuote(request);
    }

    // Check if the quote request failed
    if (quoteResponse.success === false || quoteResponse._tag) {
      console.error('Rhino: Quote request failed', quoteResponse);

      // Handle specific Rhino error types
      if (quoteResponse._tag === 'WithdrawLimitReached') {
        return {
          success: false,
          status: 422,
          error: 'Withdraw limit reached',
          message: `Withdraw limit reached. Max amount: ${quoteResponse.maxWithdrawAmount} for ${quoteResponse.token} on ${quoteResponse.chain}`,
          data: quoteResponse as any
        };
      }

      if (quoteResponse._tag === 'Unauthorized') {
        return {
          success: false,
          status: 401,
          error: 'Unauthorized',
          message: quoteResponse.message || 'Authentication failed'
        };
      }

      if (quoteResponse._tag === 'HttpApiDecodeError') {
        return {
          success: false,
          status: 400,
          error: 'Invalid request format',
          message: quoteResponse.message || 'Invalid request parameters',
          data: quoteResponse as any
        };
      }

      if (quoteResponse._tag === 'NoRouteFoundError') {
        return {
          success: false,
          status: 404,
          error: 'No route found',
          message: `No route found for ${quoteResponse.tokenIn} to ${quoteResponse.tokenOut} from ${quoteResponse.chainIn} to ${quoteResponse.chainOut}`,
          data: quoteResponse as any
        };
      }

      if (quoteResponse._tag === 'EndpointDisabledError') {
        return {
          success: false,
          status: 503,
          error: 'Service unavailable',
          message: quoteResponse.message || 'Bridge service temporarily disabled',
          data: quoteResponse as any
        };
      }

      return {
        success: false,
        status: quoteResponse.status || 500,
        error: quoteResponse.error || 'Quote request failed',
        message: quoteResponse.message || 'Quote request failed'
      };
    }

    // Format the response - use the actual quote data from the response
    let unifiedResponse: UnifiedBridgeSwapResponse;
    const quoteData = quoteResponse.data || quoteResponse;

    if (operationType === 'swap') {
      unifiedResponse = this.formatSwapQuote(quoteData);
    } else {
      unifiedResponse = this.formatBridgeQuote(quoteData);
    }

    console.log('Rhino: Quote generated successfully', {
      operationType,
      quoteId: unifiedResponse.id
    });

    return {
      success: true,
      data: unifiedResponse
    };
  }

  /**
   * Format bridge quote to unified response
   */
  private formatBridgeQuote(quote: any): UnifiedBridgeSwapResponse {
    // Handle actual Rhino bridge quote response format
    const expiresAtTimestamp = new Date(quote.expiresAt).getTime();

    return {
      id: quote.quoteId,
      fromChain: quote.chainIn,
      toChain: quote.chainOut,
      operationType: 'bridge',
      inputToken: quote.token,
      inputAmount: quote.payAmount,
      inputAmountUsd: quote.payAmountUsd?.toString(),
      outputToken: quote.token,
      outputAmount: quote.receiveAmount,
      outputAmountUsd: quote.receiveAmountUsd?.toString(),
      gasEstimate: quote.fees?.gasFee || '0',
      gasEstimateUsd: quote.fees?.gasFeeUsd?.toString(),
      timeEstimate: quote.estimatedDuration ? `${quote.estimatedDuration} minutes` : 'Unknown',
      fees: [
        {
          type: 'network' as const,
          amount: quote.fees?.gasFee || '0',
          amountUsd: quote.fees?.gasFeeUsd?.toString(),
          description: 'Gas fee'
        },
        {
          type: 'protocol' as const,
          amount: quote.fees?.platformFee || '0',
          amountUsd: quote.fees?.platformFeeUsd?.toString(),
          description: 'Platform fee'
        },
        {
          type: 'protocol' as const,
          amount: quote.fees?.percentageFee || '0',
          amountUsd: quote.fees?.percentageFeeUsd?.toString(),
          description: 'Percentage fee'
        }
      ].filter(fee => fee.amount !== '0'), // Only include non-zero fees
      expiresAt: expiresAtTimestamp,
      expiresIn: Math.max(0, expiresAtTimestamp - Date.now()),
    };
  }

  /**
   * Format bridge-swap quote to unified response
   */
  private formatSwapQuote(quote: any): UnifiedBridgeSwapResponse {
    // Handle actual Rhino bridge-swap quote response format (same as bridge format)
    const expiresAtTimestamp = new Date(quote.expiresAt).getTime();

    return {
      id: quote.quoteId,
      fromChain: quote.chainIn,
      toChain: quote.chainOut,
      operationType: 'swap',
      inputToken: quote.tokenIn || quote.token, // Bridge-swap has tokenIn, bridge has token
      inputAmount: quote.payAmount,
      inputAmountUsd: quote.payAmountUsd?.toString(),
      outputToken: quote.tokenOut || quote.token, // Bridge-swap has tokenOut, bridge has token
      outputAmount: quote.receiveAmount,
      outputAmountUsd: quote.receiveAmountUsd?.toString(),
      gasEstimate: quote.fees?.gasFee || '0',
      gasEstimateUsd: quote.fees?.gasFeeUsd?.toString(),
      timeEstimate: quote.estimatedDuration ? `${quote.estimatedDuration} minutes` : 'Unknown',
      fees: [
        {
          type: 'network' as const,
          amount: quote.fees?.gasFee || '0',
          amountUsd: quote.fees?.gasFeeUsd?.toString(),
          description: 'Gas fee'
        },
        {
          type: 'protocol' as const,
          amount: quote.fees?.platformFee || '0',
          amountUsd: quote.fees?.platformFeeUsd?.toString(),
          description: 'Platform fee'
        },
        {
          type: 'protocol' as const,
          amount: quote.fees?.percentageFee || '0',
          amountUsd: quote.fees?.percentageFeeUsd?.toString(),
          description: 'Percentage fee'
        }
      ].filter(fee => fee.amount !== '0'), // Only include non-zero fees
      expiresAt: expiresAtTimestamp,
      expiresIn: Math.max(0, expiresAtTimestamp - Date.now()),
      slippage: 0.5, // Default slippage for bridge-swaps
      priceImpact: this.calculatePriceImpact(quote),
    };
  }

  /**
   * Calculate price impact for swaps
   */
  private calculatePriceImpact(quote: any): string {
    const inputUsd = parseFloat(quote.payAmountUsd?.toString() || '0');
    const outputUsd = parseFloat(quote.receiveAmountUsd?.toString() || '0');

    if (inputUsd === 0) return '0';

    const impact = ((inputUsd - outputUsd) / inputUsd) * 100;
    return Math.abs(impact).toFixed(2);
  }

  /**
   * Commit a bridge quote
   */
  public async commitQuote(quoteId: string): Promise<RhinoApiResponse<any>> {
    console.log('Rhino: Committing quote', { quoteId });

    if (!quoteId) {
      console.error('Rhino: Missing required quoteId for commit');
      return {
        success: false,
        status: 400,
        error: 'Missing required field: quoteId',
        message: 'Missing required field: quoteId'
      };
    }

    // Use the correct endpoint format with quoteId in path
    const response = await this.makeRequest('post', `/bridge/quote/commit/${quoteId}`, {});

    // Check for specific Rhino error types
    if (response.success === false || response._tag) {
      console.error('Rhino: Failed to commit quote', {
        quoteId,
        error: response.error,
        _tag: response._tag
      });

      // Handle specific error types
      if (response._tag === 'QuoteNotFound') {
        return {
          success: false,
          status: 404,
          error: 'Quote not found',
          message: response.message || 'Quote not found or expired',
          data: response as any
        };
      }

      if (response._tag === 'InvalidRequest') {
        return {
          success: false,
          status: response.status === 429 ? 429 : 422,
          error: 'Invalid request',
          message: response.message || 'Invalid request or rate limit exceeded',
          data: response as any
        };
      }

      return {
        success: false,
        status: response.status || 500,
        error: response.error || 'Commit failed',
        message: response.message || 'Failed to commit quote'
      };
    }

    console.log('Rhino: Quote committed successfully', {
      quoteId,
      responseQuoteId: response.quoteId
    });

    return {
      success: true,
      data: response
    };
  }

  /**
   * Generate Smart Deposit addresses using Rhino.fi API
   * Endpoint: POST /bridge/deposit-addresses
   */
  public async generateSmartDeposit(
    depositChains: string[],
    destinationChain: string,
    destinationAddress: string
  ): Promise<RhinoApiResponse<any>> {
    try {
      console.log('Rhino: Generating Smart Deposit addresses', {
        depositChains,
        destinationChain,
        destinationAddress: destinationAddress.substring(0, 8) + '...'
      });

      const endpoint = '/bridge/deposit-addresses';
      const payload = {
        depositChains: depositChains,
        destinationChain: destinationChain,
        destinationAddress: destinationAddress
      };

      const response = await this.makeRequest('post', endpoint, payload);

      if (!response.success) {
        console.error('Rhino: Smart deposit generation failed', response);

        // Handle specific error types based on Rhino API documentation
        if (response.status === 400 && response._tag === 'HttpApiDecodeError') {
          return {
            success: false,
            status: 400,
            error: 'Invalid request format',
            message: response.message || 'Request validation failed',
            issues: response.issues
          };
        }

        if (response.status === 401 && response._tag === 'Unauthorized') {
          return {
            success: false,
            status: 401,
            error: 'Unauthorized',
            message: response.message || 'Invalid API key'
          };
        }

        if (response.status === 422 && response._tag === 'DepositAddressChainsNotSupported') {
          return {
            success: false,
            status: 422,
            error: 'Chains not supported',
            message: 'One or more deposit chains are not supported',
            unsupportedChains: response.chains
          };
        }

        if (response.status === 503 && response._tag === 'EndpointDisabledError') {
          return {
            success: false,
            status: 503,
            error: 'Service unavailable',
            message: response.message || 'Smart deposits endpoint is currently disabled'
          };
        }

        return {
          success: false,
          status: response.status || 500,
          error: response.error || 'Smart deposit generation failed',
          message: response.message || 'Failed to generate smart deposit addresses'
        };
      }

      console.log('Rhino: Smart deposit addresses generated successfully', {
        addressCount: response.data?.length || 0
      });

      return {
        success: true,
        data: response.data // Array of deposit address objects
      };

    } catch (error: any) {
      console.error('Rhino: Smart deposit generation error:', error);
      return {
        success: false,
        status: 500,
        error: error.message,
        message: 'Failed to generate smart deposit addresses'
      };
    }
  }

  /**
   * Get deposit address history using Rhino.fi API
   * Endpoint: GET /bridge/deposit-addresses/{depositAddress}/{depositChain}/history
   */
  public async getDepositAddressHistory(
    depositAddress: string,
    depositChain: string
  ): Promise<RhinoApiResponse<any>> {
    try {
      console.log('Rhino: Getting deposit address history', {
        depositAddress: depositAddress.substring(0, 8) + '...',
        depositChain
      });

      const endpoint = `/bridge/deposit-addresses/${depositAddress}/${depositChain}/history`;
      const response = await this.makeRequest('get', endpoint);

      if (!response.success) {
        console.error('Rhino: Deposit address history check failed', response);

        // Handle specific error types based on Rhino API documentation
        if (response.status === 400) {
          // For 400, API still returns bridge data but with failed transactions
          return {
            success: true, // Still return success as we get data
            status: 200,
            data: response.data || {
              depositAddress,
              depositChain,
              bridges: response.bridges || []
            },
            message: 'History retrieved with some failed transactions'
          };
        }

        if (response.status === 401 && response._tag === 'Unauthorized') {
          return {
            success: false,
            status: 401,
            error: 'Unauthorized',
            message: response.message || 'Invalid API key'
          };
        }

        if (response.status === 422 && response._tag === 'DepositAddressNotFound') {
          return {
            success: false,
            status: 422,
            error: 'Deposit address not found',
            message: response.message || 'The specified deposit address was not found',
            addresses: response.addresses
          };
        }

        if (response.status === 503 && response._tag === 'EndpointDisabledError') {
          return {
            success: false,
            status: 503,
            error: 'Service unavailable',
            message: response.message || 'Deposit address history endpoint is currently disabled'
          };
        }

        return {
          success: false,
          status: response.status || 500,
          error: response.error || 'History check failed',
          message: response.message || 'Failed to get deposit address history'
        };
      }

      console.log('Rhino: Deposit address history retrieved successfully', {
        bridgeCount: response.data?.bridges?.length || 0,
        depositAddress: response.data?.depositAddress?.substring(0, 8) + '...'
      });

      return {
        success: true,
        data: response.data
      };

    } catch (error: any) {
      console.error('Rhino: Deposit address history check error:', error);
      return {
        success: false,
        status: 500,
        error: error.message,
        message: 'Failed to get deposit address history'
      };
    }
  }



  /**
   * Get bridge transaction status
   */
  public async getStatus(bridgeId: string): Promise<RhinoApiResponse<any>> {
    console.log('Rhino: Getting bridge transaction status', { bridgeId });

    if (!bridgeId) {
      console.error('Rhino: Missing bridgeId for status check');
      return {
        success: false,
        status: 400,
        error: 'bridgeId is required',
        message: 'bridgeId is required'
      };
    }

    // Use the correct endpoint format with bridgeId in path
    const response = await this.makeRequest('get', `/bridge/history/bridge/${bridgeId}`);

    // Check for specific Rhino error types
    if (response.success === false || response._tag) {
      console.error('Rhino: Failed to get bridge status', {
        bridgeId,
        error: response.error,
        _tag: response._tag
      });

      // Handle specific error types
      if (response._tag === 'InvalidRequest') {
        return {
          success: false,
          status: 422,
          error: 'Invalid request',
          message: response.message || 'Invalid bridge ID or request format',
          data: response as any
        };
      }

      return {
        success: false,
        status: response.status || 500,
        error: response.error || 'Status check failed',
        message: response.message || 'Failed to get bridge status'
      };
    }

    console.log('Rhino: Bridge status retrieved successfully', {
      bridgeId,
      state: response.state,
      _id: response._id
    });

    return {
      success: true,
      data: response
    };
  }
}

export default new Rhino();

// Export types for use in other modules
export {
  RhinoApiResponse
};
