import { Request, Response } from 'express';
import StellarTrading from '../models/StellarTrading';
import StellarSDEXTrading from '../models/StellarSDEXTrading';
import OrderMatchingService from '../services/OrderMatchingService';

// Use SDEX model for main trading operations
const stellarSDEXTrading = new StellarSDEXTrading();
// Keep old model for legacy operations
const stellarTrading = new StellarTrading();
const orderMatchingService = new OrderMatchingService();


export async function getOrderById(req: Request, res: Response) {
    try {
        const result = await stellarTrading.getOrderById(req.params.orderId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}
 

export async function getOrderBook(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.getOrderBook(
            req.params.baseAsset,
            req.params.counterAsset,
            req.body.clientId || ''
        );
        
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getTradingBalance(req: Request, res: Response) {
    try {
        const result = await stellarTrading.getTradingBalance(req.body.clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getClientBalances(req: Request, res: Response) {
    try {
        const result = await stellarTrading.getTradingBalance(req.body.clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getAllOrders(req: Request, res: Response) {
    try {
        // Use SDEX to get trade history from Stellar Horizon
        const result = await stellarSDEXTrading.getTradeHistory(
            req.params.clientId,
            req.query.baseAsset as string,
            req.query.counterAsset as string
        );
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function cancelAllOrders(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.cancelAllOrders(req.body.clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getTradingPairs(req: Request, res: Response) {
    try {
        const result = await stellarTrading.getTradingPairs();
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getOrderbookPrice(req: Request, res: Response) {
    try {
        const { baseAsset, counterAsset, orderType } = req.body;
        
        // Validate required parameters
        if (!baseAsset || !counterAsset || !orderType) {
            return res.status(400).json({
                status: 400,
                message: 'Missing required parameters: baseAsset, counterAsset, and orderType are required',
                data: null
            });
        }

        if (orderType !== 'buy' && orderType !== 'sell') {
            return res.status(400).json({
                status: 400,
                message: 'Invalid orderType. Must be "buy" or "sell"',
                data: null
            });
        }
        
        const result = await stellarSDEXTrading.getPrice(
            baseAsset,
            counterAsset,
            orderType
        );
        
        // Transform SDEX result to match expected format
        const transformedResult = {
            status: result.success ? 200 : 400,
            message: result.message || (result.success ? 'Price retrieved successfully' : 'Failed to retrieve price'),
            data: result.success ? {
                price: result.price,
                baseAsset: baseAsset,
                counterAsset: counterAsset,
                orderType: orderType
            } : null
        };
        
        return res.status(transformedResult.status).json(transformedResult);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function createOffer(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.createOffer(req.body);
        
        // Transform result to match expected format
        const transformedResult = {
            status: result.status,
            message: result.message,
            data: result.data
        };
        
        return res.status(result.status).json(transformedResult);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function makePathPayment(req: Request, res: Response) {
    try {
        const result = await stellarSDEXTrading.makePathPayment(req.body);
        
        // Transform SDEX result to match expected format
        const transformedResult = {
            status: result.success ? 200 : 400,
            message: result.message || (result.success ? 'Path payment executed successfully' : 'Failed to execute path payment'),
            data: result.success ? {
                amount: result.amount,
                price: result.price,
                stellarHash: result.stellarHash
            } : null
        };
        
        return res.status(transformedResult.status).json(transformedResult);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}
export async function cancelOrder(req: Request, res: Response) {
    try {
        // Use SDEX for cancel order
        const result = await stellarSDEXTrading.cancelOrder(req.body.orderId, req.body.clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}



export async function attachClientToOrder(req: Request, res: Response) {
    try {
        const { orderId, clientId } = req.body;
        if (!orderId || !clientId) {
            return res.status(400).json({ status: 400, message: 'Order ID and Client ID are required', data: null });
        }
        const result = await stellarSDEXTrading.attachClientToOrder(orderId, clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function cacheOffers(req: Request, res: Response) {
    try {
        const { publicKey, clientId } = req.body;
        if (!publicKey || !clientId) {
            return res.status(400).json({ status: 400, message: 'Public Key and Client ID are required', data: null });
        }
        const result = await stellarSDEXTrading.cacheOffers(publicKey, clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function preflightMarketOrder(req: Request, res: Response) {
    try {
        const result = await stellarTrading.preflightMarketOrder(req.body);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getTradingHistory(req: Request, res: Response) {
    try {
        // Use SDEX for trade history
        const result = await stellarSDEXTrading.getTradeHistory(
            req.body.clientId || req.query.clientId as string,
            req.body.baseAsset || req.query.baseAsset as string,
            req.body.counterAsset || req.query.counterAsset as string
        );
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getUserOrders(req: Request, res: Response) {
    try {
        // Use SDEX for order history
        const result = await stellarSDEXTrading.getOrderHistory(
            req.body.clientId,
            req.body.baseAsset,
            req.body.counterAsset
        );
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function healthCheck(req: Request, res: Response) {
    try {
        return res.status(200).json({
            status: 200,
            message: 'Trading service is healthy',
            data: { service: 'stellar-trading', timestamp: new Date().toISOString(), version: '1.0.0' }
        });
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: 'Trading service is unhealthy', data: null });
    }
}

export async function getTradingAccountPublicKey(req: Request, res: Response) {
    try {
        const { clientId } = req.body;
        const result = await stellarSDEXTrading.getTradingAccountPublicKey(clientId);
        if (result) {
            return res.status(200).json({ status: 200, message: 'Trading account retrieved', data: result });
        } else {
            return res.status(404).json({ status: 404, message: 'Trading account not found', data: null });
        }
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}   

export async function getTradingAccountBalances(req: Request, res: Response) {
    try {
        const { clientId } = req.body;
        const balances = await stellarSDEXTrading.getTradingAccountBalances(clientId);
        return res.status(200).json({ status: 200, message: 'Trading account balances retrieved', data: balances });
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}   
export async function createPendingRailOrder(req: Request, res: Response) {
    try {
        const result = await stellarTrading.createPendingRailOrder({
            ...req.body,
            expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : undefined
        });
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}



export async function getPendingRailOrders(req: Request, res: Response) {
    try {
        const result = await stellarTrading.getPendingRailOrders(req.body.clientId);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function getRailOrderRate(req: Request, res: Response) {
    try {
        const result = await stellarTrading.getRailOrderRate(req.body);
        return res.status(result.status).json(result);
    } catch (error: any) {
        return res.status(500).json({ status: 500, message: error.message, data: null });
    }
}

export async function triggerOrderMatching(req: Request, res: Response) {
    try {
        console.log('🔄 Manual order matching triggered via API');
        await orderMatchingService.triggerManualMatching();
        return res.status(200).json({ 
            status: 200, 
            message: 'Order matching completed successfully',
            data: { triggeredAt: new Date().toISOString() }
        });
    } catch (error: any) {
        console.error('❌ Manual order matching failed:', error);
        return res.status(500).json({ 
            status: 500, 
            message: 'Order matching failed', 
            data: { error: error.message }
        });
    }
}
